# ToolHane Blog .htaccess

# Enable URL Rewriting
RewriteEngine On

# Security Headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
</IfModule>

# Hide sensitive files
<Files "config.php">
    Order allow,deny
    Deny from all
</Files>

<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Prevent access to includes directory
<IfModule mod_rewrite.c>
    RewriteRule ^includes/ - [F,L]
</IfModule>

# Custom Error Pages
ErrorDocument 404 /blog/404.php
ErrorDocument 500 /blog/500.php

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Browser Caching
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
</IfModule>

# Security - Prevent PHP execution in uploads directory
<Directory "assets/images/uploads">
    <Files "*.php">
        Order allow,deny
        Deny from all
    </Files>
</Directory>

# Prevent directory browsing
Options -Indexes

# Force HTTPS (uncomment if you have SSL)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Remove trailing slash from URLs
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{THE_REQUEST} /+([^?\s]*?)/?(\?[^\s]*)?\s
RewriteRule ^(.*)$ %1 [R=301,L]

# Pretty URLs for posts
RewriteRule ^post/([^/]+)/?$ post.php?slug=$1 [L,QSA]

# Pretty URLs for categories
RewriteRule ^category/([^/]+)/?$ category.php?slug=$1 [L,QSA]

# Pretty URLs for search
RewriteRule ^search/?$ search.php [L,QSA]

# Admin panel protection (optional - uncomment to restrict by IP)
# <Directory "admin">
#     Order deny,allow
#     Deny from all
#     Allow from 127.0.0.1
#     Allow from YOUR_IP_ADDRESS
# </Directory>
