<?php
/**
 * Yardımcı Fonksiyonlar
 */

/**
 * XSS koruması için HTML karakterleri temizle
 */
function clean($data) {
    if (is_array($data)) {
        return array_map('clean', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * CSRF token oluştur
 */
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

/**
 * CSRF token doğrula
 */
function verifyCSRFToken($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

/**
 * Admin girişi kontrol et
 */
function isLoggedIn() {
    return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
}

/**
 * Admin girişi zorunlu kıl
 */
function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: login.php');
        exit;
    }
}

/**
 * URL slug oluştur
 */
function createSlug($text) {
    // Türkçe karakterleri değiştir
    $turkish = ['ç', 'ğ', 'ı', 'ö', 'ş', 'ü', 'Ç', 'Ğ', 'I', 'İ', 'Ö', 'Ş', 'Ü'];
    $english = ['c', 'g', 'i', 'o', 's', 'u', 'c', 'g', 'i', 'i', 'o', 's', 'u'];
    $text = str_replace($turkish, $english, $text);
    
    // Küçük harfe çevir
    $text = strtolower($text);
    
    // Özel karakterleri kaldır
    $text = preg_replace('/[^a-z0-9\s-]/', '', $text);
    
    // Boşlukları tire ile değiştir
    $text = preg_replace('/[\s-]+/', '-', $text);
    
    // Başındaki ve sonundaki tireleri kaldır
    return trim($text, '-');
}

/**
 * Tarih formatla
 */
function formatDate($date, $format = 'd.m.Y H:i') {
    return date($format, strtotime($date));
}

/**
 * Göreli tarih (2 saat önce, 3 gün önce vs.)
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'az önce';
    if ($time < 3600) return floor($time/60) . ' dakika önce';
    if ($time < 86400) return floor($time/3600) . ' saat önce';
    if ($time < 2592000) return floor($time/86400) . ' gün önce';
    if ($time < 31536000) return floor($time/2592000) . ' ay önce';
    
    return floor($time/31536000) . ' yıl önce';
}

/**
 * Metin kısalt
 */
function excerpt($text, $length = 150) {
    $text = strip_tags($text);
    if (strlen($text) <= $length) {
        return $text;
    }
    
    $text = substr($text, 0, $length);
    $lastSpace = strrpos($text, ' ');
    if ($lastSpace !== false) {
        $text = substr($text, 0, $lastSpace);
    }
    
    return $text . '...';
}

/**
 * Sayfalama hesapla
 */
function calculatePagination($currentPage, $totalItems, $itemsPerPage) {
    $totalPages = ceil($totalItems / $itemsPerPage);
    $offset = ($currentPage - 1) * $itemsPerPage;
    
    return [
        'current_page' => $currentPage,
        'total_pages' => $totalPages,
        'total_items' => $totalItems,
        'items_per_page' => $itemsPerPage,
        'offset' => $offset,
        'has_prev' => $currentPage > 1,
        'has_next' => $currentPage < $totalPages,
        'prev_page' => $currentPage > 1 ? $currentPage - 1 : null,
        'next_page' => $currentPage < $totalPages ? $currentPage + 1 : null
    ];
}

/**
 * Flash mesaj ekle
 */
function setFlashMessage($type, $message) {
    $_SESSION['flash_messages'][] = [
        'type' => $type,
        'message' => $message
    ];
}

/**
 * Flash mesajları al ve temizle
 */
function getFlashMessages() {
    $messages = $_SESSION['flash_messages'] ?? [];
    unset($_SESSION['flash_messages']);
    return $messages;
}

/**
 * Dosya yükleme
 */
function uploadFile($file, $uploadDir = UPLOAD_DIR) {
    if (!isset($file['error']) || is_array($file['error'])) {
        throw new RuntimeException('Geçersiz dosya parametresi.');
    }
    
    switch ($file['error']) {
        case UPLOAD_ERR_OK:
            break;
        case UPLOAD_ERR_NO_FILE:
            throw new RuntimeException('Dosya seçilmedi.');
        case UPLOAD_ERR_INI_SIZE:
        case UPLOAD_ERR_FORM_SIZE:
            throw new RuntimeException('Dosya boyutu çok büyük.');
        default:
            throw new RuntimeException('Bilinmeyen dosya yükleme hatası.');
    }
    
    if ($file['size'] > MAX_FILE_SIZE) {
        throw new RuntimeException('Dosya boyutu çok büyük.');
    }
    
    $finfo = new finfo(FILEINFO_MIME_TYPE);
    $mimeType = $finfo->file($file['tmp_name']);
    $allowedTypes = [
        'image/jpeg' => 'jpg',
        'image/png' => 'png',
        'image/gif' => 'gif',
        'image/webp' => 'webp'
    ];
    
    if (!array_key_exists($mimeType, $allowedTypes)) {
        throw new RuntimeException('Geçersiz dosya türü.');
    }
    
    $extension = $allowedTypes[$mimeType];
    $filename = sprintf('%s.%s', uniqid(), $extension);
    $destination = $uploadDir . $filename;
    
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    if (!move_uploaded_file($file['tmp_name'], $destination)) {
        throw new RuntimeException('Dosya yüklenemedi.');
    }
    
    return $filename;
}

/**
 * Meta title oluştur
 */
function getMetaTitle($title = '') {
    if (empty($title)) {
        return SITE_NAME;
    }
    return $title . ' - ' . SITE_NAME;
}

/**
 * Breadcrumb oluştur
 */
function getBreadcrumb($items) {
    $breadcrumb = '<nav class="text-sm text-gray-600 mb-4">';
    $breadcrumb .= '<ol class="flex space-x-2">';
    
    foreach ($items as $index => $item) {
        $isLast = $index === count($items) - 1;
        
        if ($isLast) {
            $breadcrumb .= '<li class="text-gray-900">' . clean($item['title']) . '</li>';
        } else {
            $breadcrumb .= '<li><a href="' . clean($item['url']) . '" class="hover:text-primary-600">' . clean($item['title']) . '</a></li>';
            $breadcrumb .= '<li class="text-gray-400">/</li>';
        }
    }
    
    $breadcrumb .= '</ol>';
    $breadcrumb .= '</nav>';
    
    return $breadcrumb;
}
