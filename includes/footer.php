    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 dark:bg-gray-950 text-white mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Logo & Description -->
                <div class="md:col-span-2">
                    <div class="flex items-center mb-4">
                        <span class="text-2xl font-bold text-primary-400">ToolHane Blog</span>
                    </div>
                    <p class="text-gray-300 mb-4 max-w-md">
                        ToolHane'in resmi blog sitesi. Teknoloji, araçlar ve dijital dünya hakkında güncel içerikler.
                    </p>
                    <div class="flex space-x-4">
                        <a href="https://www.toolhane.com" target="_blank" class="text-gray-400 hover:text-primary-400 transition-colors">
                            <i class="fas fa-globe text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-primary-400 transition-colors">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-primary-400 transition-colors">
                            <i class="fab fa-facebook text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-primary-400 transition-colors">
                            <i class="fab fa-instagram text-xl"></i>
                        </a>
                    </div>
                </div>

                <!-- Kategoriler -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Kategoriler</h3>
                    <ul class="space-y-2">
                        <?php
                        try {
                            $db = Database::getInstance();
                            $categories = $db->fetchAll("SELECT name, slug FROM categories ORDER BY name");
                            foreach ($categories as $category):
                        ?>
                            <li>
                                <a href="category.php?slug=<?php echo clean($category['slug']); ?>" 
                                   class="text-gray-300 hover:text-primary-400 transition-colors">
                                    <?php echo clean($category['name']); ?>
                                </a>
                            </li>
                        <?php 
                            endforeach;
                        } catch (Exception $e) {
                            // Kategoriler yüklenemezse varsayılan kategoriler göster
                        ?>
                            <li><a href="#" class="text-gray-300 hover:text-primary-400 transition-colors">Genel</a></li>
                            <li><a href="#" class="text-gray-300 hover:text-primary-400 transition-colors">Teknoloji</a></li>
                            <li><a href="#" class="text-gray-300 hover:text-primary-400 transition-colors">Araçlar</a></li>
                            <li><a href="#" class="text-gray-300 hover:text-primary-400 transition-colors">Duyurular</a></li>
                        <?php } ?>
                    </ul>
                </div>

                <!-- Hızlı Erişim -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Hızlı Erişim</h3>
                    <ul class="space-y-2">
                        <li>
                            <a href="index.php" class="text-gray-300 hover:text-primary-400 transition-colors">
                                Ana Sayfa
                            </a>
                        </li>
                        <li>
                            <a href="https://www.toolhane.com" target="_blank" class="text-gray-300 hover:text-primary-400 transition-colors">
                                ToolHane Ana Site
                            </a>
                        </li>
                        <li>
                            <a href="https://www.toolhane.com/youtube" target="_blank" class="text-gray-300 hover:text-primary-400 transition-colors">
                                YouTube Araçları
                            </a>
                        </li>
                        <li>
                            <a href="https://www.toolhane.com/yasam" target="_blank" class="text-gray-300 hover:text-primary-400 transition-colors">
                                Yaşam Araçları
                            </a>
                        </li>
                        <li>
                            <a href="https://www.toolhane.com/gorsel" target="_blank" class="text-gray-300 hover:text-primary-400 transition-colors">
                                Görsel Araçları
                            </a>
                        </li>
                        <li>
                            <a href="admin/login.php" class="text-gray-300 hover:text-primary-400 transition-colors">
                                Admin Paneli
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Copyright -->
            <div class="border-t border-gray-800 mt-8 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-400 text-sm">
                        &copy; <?php echo date('Y'); ?> ToolHane Blog. Tüm hakları saklıdır.
                    </p>
                    <div class="flex space-x-6 mt-4 md:mt-0">
                        <a href="#" class="text-gray-400 hover:text-primary-400 text-sm transition-colors">
                            Gizlilik Politikası
                        </a>
                        <a href="#" class="text-gray-400 hover:text-primary-400 text-sm transition-colors">
                            Kullanım Şartları
                        </a>
                        <a href="#" class="text-gray-400 hover:text-primary-400 text-sm transition-colors">
                            İletişim
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
    
    <!-- Theme Script -->
    <script>
        // Theme toggle functionality
        const themeToggle = document.getElementById('theme-toggle');
        const mobileThemeToggle = document.getElementById('mobile-theme-toggle');
        
        function toggleDarkMode() {
            if (document.documentElement.classList.contains('dark')) {
                document.documentElement.classList.remove('dark');
                localStorage.theme = 'light';
            } else {
                document.documentElement.classList.add('dark');
                localStorage.theme = 'dark';
            }
        }
        
        if (themeToggle) {
            themeToggle.addEventListener('click', toggleDarkMode);
        }
        
        if (mobileThemeToggle) {
            mobileThemeToggle.addEventListener('click', toggleDarkMode);
        }
        
        // Set initial theme
        if (localStorage.theme === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
        
        // Mobile menu toggle
        const mobileMenuButton = document.querySelector('.mobile-menu-button');
        const mobileMenu = document.querySelector('.mobile-menu');
        
        if (mobileMenuButton && mobileMenu) {
            mobileMenuButton.addEventListener('click', () => {
                mobileMenu.classList.toggle('hidden');
            });
            
            // Close mobile menu when clicking outside
            document.addEventListener('click', (e) => {
                if (!mobileMenuButton.contains(e.target) && !mobileMenu.contains(e.target)) {
                    mobileMenu.classList.add('hidden');
                }
            });
        }
        
        // Search functionality
        function setupSearch(inputId, resultsId) {
            const searchInput = document.getElementById(inputId);
            const searchResults = document.getElementById(resultsId);
            
            if (!searchInput || !searchResults) return;
            
            let searchTimeout;
            
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                const query = this.value.trim();
                
                if (query.length >= 2) {
                    searchTimeout = setTimeout(() => {
                        performSearch(query, searchResults);
                    }, 300);
                } else {
                    searchResults.innerHTML = '';
                    searchResults.classList.add('hidden');
                }
            });
            
            // Hide search results when clicking outside
            document.addEventListener('click', function(e) {
                if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
                    searchResults.classList.add('hidden');
                }
            });
        }
        
        function performSearch(query, resultsContainer) {
            resultsContainer.innerHTML = '<div class="p-3 text-gray-500 dark:text-gray-400">Aranıyor...</div>';
            resultsContainer.classList.remove('hidden');
            
            fetch(`search.php?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    displaySearchResults(data.results || [], resultsContainer, query);
                })
                .catch(error => {
                    console.error('Search error:', error);
                    resultsContainer.innerHTML = '<div class="p-3 text-red-500">Arama sırasında hata oluştu.</div>';
                });
        }
        
        function displaySearchResults(results, container, query) {
            if (results.length === 0) {
                container.innerHTML = '<div class="p-3 text-gray-500 dark:text-gray-400">Sonuç bulunamadı.</div>';
                return;
            }
            
            let html = '';
            results.forEach(result => {
                html += `
                    <a href="${result.url}" class="block p-3 hover:bg-gray-50 dark:hover:bg-gray-700 border-b border-gray-100 dark:border-gray-600 last:border-0">
                        <div class="font-medium text-gray-900 dark:text-gray-100">${result.title}</div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">${result.excerpt}</div>
                    </a>
                `;
            });
            
            // Add "Show all results" link
            html += `
                <a href="search.php?q=${encodeURIComponent(query)}" class="block p-3 text-center text-primary-600 dark:text-primary-400 font-medium hover:bg-gray-50 dark:hover:bg-gray-700">
                    Tüm sonuçları göster
                </a>
            `;
            
            container.innerHTML = html;
            container.classList.remove('hidden');
        }
        
        // Initialize search for both desktop and mobile
        setupSearch('desktop-search-input', 'desktop-search-results');
        setupSearch('mobile-search-input', 'mobile-search-results');
    </script>
    
    <?php if (isset($additional_scripts)): ?>
        <?php echo $additional_scripts; ?>
    <?php endif; ?>
</body>
</html>
