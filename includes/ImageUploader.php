<?php

class ImageUploader {
    private $db;
    private $uploadPath;
    private $allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
    private $maxFileSize = 5242880; // 5MB
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->uploadPath = __DIR__ . '/../uploads/posts/';
        
        // Upload ayarlarını veritabanından al
        $this->loadSettings();
    }
    
    private function loadSettings() {
        try {
            $settings = $this->db->fetchAll("SELECT setting_key, setting_value FROM upload_settings");
            foreach ($settings as $setting) {
                switch ($setting['setting_key']) {
                    case 'max_file_size':
                        $this->maxFileSize = (int)$setting['setting_value'];
                        break;
                    case 'allowed_extensions':
                        $extensions = explode(',', $setting['setting_value']);
                        $this->allowedTypes = array_map(function($ext) {
                            return $this->getMimeType($ext);
                        }, $extensions);
                        break;
                }
            }
        } catch (Exception $e) {
            // Varsayılan ayarları kullan
        }
    }
    
    private function getMimeType($extension) {
        $mimeTypes = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'webp' => 'image/webp',
            'gif' => 'image/gif'
        ];
        return $mimeTypes[strtolower($extension)] ?? 'image/jpeg';
    }
    
    public function uploadImage($file, $postId = null, $altText = '', $caption = '') {
        try {
            // Dosya kontrolü
            if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
                throw new Exception('Geçersiz dosya');
            }
            
            // Boyut kontrolü
            if ($file['size'] > $this->maxFileSize) {
                throw new Exception('Dosya boyutu çok büyük (Max: ' . ($this->maxFileSize / 1024 / 1024) . 'MB)');
            }
            
            // Tip kontrolü
            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $mimeType = finfo_file($finfo, $file['tmp_name']);
            finfo_close($finfo);
            
            if (!in_array($mimeType, $this->allowedTypes)) {
                throw new Exception('Desteklenmeyen dosya tipi');
            }
            
            // Resim boyutlarını al
            $imageInfo = getimagesize($file['tmp_name']);
            if (!$imageInfo) {
                throw new Exception('Geçersiz resim dosyası');
            }
            
            $width = $imageInfo[0];
            $height = $imageInfo[1];
            
            // Benzersiz dosya adı oluştur
            $extension = $this->getExtensionFromMime($mimeType);
            $filename = uniqid() . '_' . time() . '.' . $extension;
            
            // Yıl/ay klasörü oluştur
            $dateFolder = date('Y/m');
            $this->createDirectories($dateFolder);
            
            // Dosya yolları
            $originalPath = $this->uploadPath . 'original/' . $dateFolder . '/' . $filename;
            $relativePath = 'uploads/posts/original/' . $dateFolder . '/' . $filename;
            
            // Orijinal dosyayı kaydet
            if (!move_uploaded_file($file['tmp_name'], $originalPath)) {
                throw new Exception('Dosya yüklenemedi');
            }
            
            // Farklı boyutlarda kopyalar oluştur
            $this->createResizedImages($originalPath, $filename, $dateFolder);
            
            // Veritabanına kaydet (sadece post_id varsa)
            $imageId = null;
            if ($postId) {
                $imageId = $this->saveToDatabase($postId, $filename, $file['name'], $relativePath,
                                               $file['size'], $mimeType, $width, $height, $altText, $caption);
            }
            
            return [
                'success' => true,
                'image_id' => $imageId,
                'filename' => $filename,
                'path' => $relativePath,
                'thumbnail' => 'uploads/posts/thumbnails/' . $dateFolder . '/' . $filename,
                'medium' => 'uploads/posts/medium/' . $dateFolder . '/' . $filename,
                'large' => 'uploads/posts/large/' . $dateFolder . '/' . $filename,
                'width' => $width,
                'height' => $height
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    private function getExtensionFromMime($mimeType) {
        $extensions = [
            'image/jpeg' => 'jpg',
            'image/png' => 'png',
            'image/webp' => 'webp',
            'image/gif' => 'gif'
        ];
        return $extensions[$mimeType] ?? 'jpg';
    }
    
    private function createDirectories($dateFolder) {
        $directories = ['original', 'thumbnails', 'medium', 'large'];
        foreach ($directories as $dir) {
            $path = $this->uploadPath . $dir . '/' . $dateFolder;
            if (!is_dir($path)) {
                mkdir($path, 0755, true);
            }
        }
    }
    
    private function createResizedImages($originalPath, $filename, $dateFolder) {
        // Thumbnail (300x200)
        $this->resizeImage($originalPath, 
                          $this->uploadPath . 'thumbnails/' . $dateFolder . '/' . $filename, 
                          300, 200);
        
        // Medium (600x400)
        $this->resizeImage($originalPath, 
                          $this->uploadPath . 'medium/' . $dateFolder . '/' . $filename, 
                          600, 400);
        
        // Large (1200x800)
        $this->resizeImage($originalPath, 
                          $this->uploadPath . 'large/' . $dateFolder . '/' . $filename, 
                          1200, 800);
    }
    
    private function resizeImage($sourcePath, $destPath, $maxWidth, $maxHeight) {
        $imageInfo = getimagesize($sourcePath);
        $sourceWidth = $imageInfo[0];
        $sourceHeight = $imageInfo[1];
        $mimeType = $imageInfo['mime'];
        
        // Orantılı boyutlandırma hesapla
        $ratio = min($maxWidth / $sourceWidth, $maxHeight / $sourceHeight);
        $newWidth = round($sourceWidth * $ratio);
        $newHeight = round($sourceHeight * $ratio);
        
        // Kaynak resmi yükle
        switch ($mimeType) {
            case 'image/jpeg':
                $sourceImage = imagecreatefromjpeg($sourcePath);
                break;
            case 'image/png':
                $sourceImage = imagecreatefrompng($sourcePath);
                break;
            case 'image/webp':
                $sourceImage = imagecreatefromwebp($sourcePath);
                break;
            case 'image/gif':
                $sourceImage = imagecreatefromgif($sourcePath);
                break;
            default:
                return false;
        }
        
        // Yeni resim oluştur
        $destImage = imagecreatetruecolor($newWidth, $newHeight);
        
        // PNG ve GIF için şeffaflığı koru
        if ($mimeType == 'image/png' || $mimeType == 'image/gif') {
            imagealphablending($destImage, false);
            imagesavealpha($destImage, true);
            $transparent = imagecolorallocatealpha($destImage, 255, 255, 255, 127);
            imagefilledrectangle($destImage, 0, 0, $newWidth, $newHeight, $transparent);
        }
        
        // Resmi yeniden boyutlandır
        imagecopyresampled($destImage, $sourceImage, 0, 0, 0, 0, 
                          $newWidth, $newHeight, $sourceWidth, $sourceHeight);
        
        // Hedef klasörü oluştur
        $destDir = dirname($destPath);
        if (!is_dir($destDir)) {
            mkdir($destDir, 0755, true);
        }
        
        // Resmi kaydet
        switch ($mimeType) {
            case 'image/jpeg':
                imagejpeg($destImage, $destPath, 85);
                break;
            case 'image/png':
                imagepng($destImage, $destPath);
                break;
            case 'image/webp':
                imagewebp($destImage, $destPath, 85);
                break;
            case 'image/gif':
                imagegif($destImage, $destPath);
                break;
        }
        
        // Belleği temizle
        imagedestroy($sourceImage);
        imagedestroy($destImage);
        
        return true;
    }
    
    private function saveToDatabase($postId, $filename, $originalFilename, $filePath,
                                   $fileSize, $mimeType, $width, $height, $altText, $caption) {
        // Featured image için post_id null olabilir
        $data = [
            'post_id' => $postId, // null olabilir
            'filename' => $filename,
            'original_filename' => $originalFilename,
            'file_path' => $filePath,
            'file_size' => $fileSize,
            'mime_type' => $mimeType,
            'width' => $width,
            'height' => $height,
            'alt_text' => $altText,
            'caption' => $caption
        ];

        return $this->db->insert('post_images', $data);
    }
    
    public function getPostImages($postId) {
        return $this->db->fetchAll(
            "SELECT * FROM post_images WHERE post_id = ? ORDER BY sort_order ASC, created_at ASC", 
            [$postId]
        );
    }
    
    public function deleteImage($imageId) {
        try {
            // Resim bilgilerini al
            $image = $this->db->fetch("SELECT * FROM post_images WHERE id = ?", [$imageId]);
            if (!$image) {
                throw new Exception('Resim bulunamadı');
            }
            
            // Dosyaları sil
            $this->deleteImageFiles($image['filename'], $image['file_path']);
            
            // Veritabanından sil
            $this->db->delete('post_images', 'id = ?', [$imageId]);
            
            return ['success' => true];
            
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    private function deleteImageFiles($filename, $originalPath) {
        // Orijinal dosya yolundan tarih klasörünü çıkar
        $pathParts = explode('/', $originalPath);
        $dateFolder = $pathParts[count($pathParts) - 3] . '/' . $pathParts[count($pathParts) - 2];
        
        $sizes = ['original', 'thumbnails', 'medium', 'large'];
        foreach ($sizes as $size) {
            $filePath = $this->uploadPath . $size . '/' . $dateFolder . '/' . $filename;
            if (file_exists($filePath)) {
                unlink($filePath);
            }
        }
    }
}
