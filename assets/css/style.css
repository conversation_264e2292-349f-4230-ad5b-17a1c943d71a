/* ToolHane Blog CSS */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

:root {
    --primary-50: #f0f9ff;
    --primary-100: #e0f2fe;
    --primary-200: #bae6fd;
    --primary-300: #7dd3fc;
    --primary-400: #38bdf8;
    --primary-500: #0ea5e9;
    --primary-600: #0284c7;
    --primary-700: #0369a1;
    --primary-800: #075985;
    --primary-900: #0c4a6e;
    
    --secondary-50: #f5f3ff;
    --secondary-100: #ede9fe;
    --secondary-200: #ddd6fe;
    --secondary-300: #c4b5fd;
    --secondary-400: #a78bfa;
    --secondary-500: #8b5cf6;
    --secondary-600: #7c3aed;
    --secondary-700: #6d28d9;
    --secondary-800: #5b21b6;
    --secondary-900: #4c1d95;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #374151;
    background-color: #f9fafb;
}

/* Header Styles */
.header {
    background: white;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 50;
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
}

.logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-600);
    text-decoration: none;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    text-decoration: none;
    color: #374151;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: var(--primary-600);
}

/* Mobile Menu */
.mobile-menu-button {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
}

.mobile-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    padding: 1rem;
}

.mobile-menu.active {
    display: block;
}

/* Main Content */
.main-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 2rem;
}

.content {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    padding: 2rem;
}

.sidebar {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    height: fit-content;
}

/* Blog Post Styles */
.post-card {
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 2rem;
    margin-bottom: 2rem;
}

.post-card:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.post-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #111827;
    margin-bottom: 0.5rem;
}

.post-title a {
    text-decoration: none;
    color: inherit;
    transition: color 0.3s ease;
}

.post-title a:hover {
    color: var(--primary-600);
}

.post-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 1rem;
}

.post-excerpt {
    color: #4b5563;
    margin-bottom: 1rem;
    line-height: 1.7;
}

.read-more {
    display: inline-block;
    color: var(--primary-600);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.read-more:hover {
    color: var(--primary-700);
}

/* Sidebar Styles */
.sidebar-section {
    margin-bottom: 2rem;
}

.sidebar-section:last-child {
    margin-bottom: 0;
}

.sidebar-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #111827;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-600);
}

.popular-post {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.popular-post:last-child {
    margin-bottom: 0;
    border-bottom: none;
}

.popular-post-content h4 {
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.4;
    margin-bottom: 0.25rem;
}

.popular-post-content h4 a {
    text-decoration: none;
    color: #111827;
    transition: color 0.3s ease;
}

.popular-post-content h4 a:hover {
    color: var(--primary-600);
}

.popular-post-date {
    font-size: 0.75rem;
    color: #6b7280;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 2rem;
}

.pagination a,
.pagination span {
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    text-decoration: none;
    color: #374151;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background-color: var(--primary-50);
    border-color: var(--primary-300);
    color: var(--primary-700);
}

.pagination .current {
    background-color: var(--primary-600);
    border-color: var(--primary-600);
    color: white;
}

/* Comments */
.comments-section {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid #e5e7eb;
}

.comment {
    background: #f9fafb;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.comment-author {
    font-weight: 600;
    color: #111827;
    margin-bottom: 0.25rem;
}

.comment-date {
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 0.75rem;
}

.comment-content {
    line-height: 1.7;
}

/* Forms */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

.form-input,
.form-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: border-color 0.3s ease;
}

.form-input:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.375rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--primary-600);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-700);
}

/* Footer */
.footer {
    background: #1f2937;
    color: #d1d5db;
    padding: 2rem 0;
    margin-top: 4rem;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .mobile-menu-button {
        display: block;
    }
    
    .main-container {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 1rem;
    }
    
    .content,
    .sidebar {
        padding: 1rem;
    }
    
    .post-title {
        font-size: 1.25rem;
    }
    
    .post-meta {
        flex-direction: column;
        gap: 0.25rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #111827;
        color: #f3f4f6;
    }
    
    .header {
        background: #1f2937;
    }
    
    .content,
    .sidebar {
        background: #1f2937;
    }
    
    .post-title {
        color: #f9fafb;
    }
    
    .comment {
        background: #374151;
    }
}
