<?php
require_once 'config.php';

// XML header
header('Content-Type: application/xml; charset=utf-8');

// Veritabanı bağlantısı
$db = Database::getInstance();

echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

// Ana sayfa
echo '<url>' . "\n";
echo '<loc>' . SITE_URL . '/</loc>' . "\n";
echo '<lastmod>' . date('Y-m-d') . '</lastmod>' . "\n";
echo '<changefreq>daily</changefreq>' . "\n";
echo '<priority>1.0</priority>' . "\n";
echo '</url>' . "\n";

try {
    // Blog yazıları
    $posts = $db->fetchAll("
        SELECT slug, updated_at 
        FROM posts 
        WHERE status = 'published' 
        ORDER BY updated_at DESC
    ");
    
    foreach ($posts as $post) {
        echo '<url>' . "\n";
        echo '<loc>' . SITE_URL . '/post.php?slug=' . htmlspecialchars($post['slug']) . '</loc>' . "\n";
        echo '<lastmod>' . date('Y-m-d', strtotime($post['updated_at'])) . '</lastmod>' . "\n";
        echo '<changefreq>weekly</changefreq>' . "\n";
        echo '<priority>0.8</priority>' . "\n";
        echo '</url>' . "\n";
    }
    
    // Kategoriler
    $categories = $db->fetchAll("SELECT slug FROM categories ORDER BY name");
    
    foreach ($categories as $category) {
        echo '<url>' . "\n";
        echo '<loc>' . SITE_URL . '/category.php?slug=' . htmlspecialchars($category['slug']) . '</loc>' . "\n";
        echo '<lastmod>' . date('Y-m-d') . '</lastmod>' . "\n";
        echo '<changefreq>weekly</changefreq>' . "\n";
        echo '<priority>0.6</priority>' . "\n";
        echo '</url>' . "\n";
    }
    
} catch (Exception $e) {
    error_log("Sitemap error: " . $e->getMessage());
}

// Arama sayfası
echo '<url>' . "\n";
echo '<loc>' . SITE_URL . '/search.php</loc>' . "\n";
echo '<lastmod>' . date('Y-m-d') . '</lastmod>' . "\n";
echo '<changefreq>monthly</changefreq>' . "\n";
echo '<priority>0.4</priority>' . "\n";
echo '</url>' . "\n";

echo '</urlset>';
?>
