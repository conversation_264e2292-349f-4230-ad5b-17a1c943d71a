<?php
require_once 'config.php';

// Arama sorgusu
$query = isset($_GET['q']) ? clean($_GET['q']) : '';
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$limit = POSTS_PER_PAGE;
$offset = ($page - 1) * $limit;

// Sayfa bilgileri
$page_title = !empty($query) ? "Arama: $query" : 'Arama';
$page_description = !empty($query) ? "$query için arama sonuçları" : 'Blog içinde arama yapın';

$results = [];
$total_results = 0;
$pagination = ['total_pages' => 1, 'current_page' => 1];

// Veritabanı bağlantısı
$db = Database::getInstance();

// AJAX isteği mi?
$is_ajax = isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';

if (!empty($query) && strlen($query) >= 2) {
    try {
        // Toplam sonuç sayısını al
        $count_sql = "
            SELECT COUNT(*) as total
            FROM posts p 
            WHERE p.status = 'published' 
            AND (p.title LIKE ? OR p.content LIKE ? OR p.excerpt LIKE ?)
        ";
        $search_term = '%' . $query . '%';
        $total_results = $db->fetch($count_sql, [$search_term, $search_term, $search_term])['total'];
        
        // Arama sonuçlarını al
        $search_sql = "
            SELECT p.*, u.full_name as author_name, u.username as author_username,
                   (SELECT COUNT(*) FROM comments c WHERE c.post_id = p.id AND c.status = 'approved') as comment_count,
                   MATCH(p.title, p.content) AGAINST(? IN NATURAL LANGUAGE MODE) as relevance
            FROM posts p 
            LEFT JOIN users u ON p.author_id = u.id 
            WHERE p.status = 'published' 
            AND (p.title LIKE ? OR p.content LIKE ? OR p.excerpt LIKE ?)
            ORDER BY relevance DESC, p.created_at DESC
            LIMIT ? OFFSET ?
        ";
        
        $results = $db->fetchAll($search_sql, [$query, $search_term, $search_term, $search_term, $limit, $offset]);
        
        // Sayfalama hesapla
        $pagination = calculatePagination($page, $total_results, $limit);
        
    } catch (Exception $e) {
        error_log("Search error: " . $e->getMessage());
        $results = [];
        $total_results = 0;
    }
}

// AJAX isteği ise JSON döndür
if ($is_ajax) {
    header('Content-Type: application/json');
    
    $ajax_results = [];
    foreach (array_slice($results, 0, 5) as $result) { // AJAX için sadece ilk 5 sonuç
        $ajax_results[] = [
            'title' => $result['title'],
            'excerpt' => excerpt($result['excerpt'] ?: $result['content'], 100),
            'url' => 'post.php?slug=' . $result['slug']
        ];
    }
    
    echo json_encode([
        'success' => true,
        'results' => $ajax_results,
        'total' => $total_results
    ]);
    exit;
}

// Header'ı dahil et
include 'includes/header.php';
?>

<!-- Search Page -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Search Header -->
    <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            <?php if (!empty($query)): ?>
                "<?php echo clean($query); ?>" için Arama Sonuçları
            <?php else: ?>
                Blog Arama
            <?php endif; ?>
        </h1>
        
        <!-- Search Form -->
        <div class="max-w-2xl mx-auto">
            <form method="GET" class="relative">
                <input type="text" 
                       name="q" 
                       value="<?php echo clean($query); ?>"
                       placeholder="Blog yazılarında ara..."
                       class="w-full px-4 py-3 pl-12 pr-4 text-lg border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                    <i class="fas fa-search text-gray-400 dark:text-gray-500"></i>
                </div>
                <button type="submit" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <span class="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors">
                        Ara
                    </span>
                </button>
            </form>
        </div>
        
        <?php if (!empty($query)): ?>
            <p class="text-gray-600 dark:text-gray-400 mt-4">
                <?php echo number_format($total_results); ?> sonuç bulundu
            </p>
        <?php endif; ?>
    </div>
    
    <!-- Search Results -->
    <?php if (!empty($query)): ?>
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Results -->
            <div class="lg:col-span-2">
                <?php if (empty($results)): ?>
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-8 text-center">
                        <i class="fas fa-search text-4xl text-gray-300 dark:text-gray-600 mb-4"></i>
                        <h3 class="text-xl font-semibold text-gray-600 dark:text-gray-400 mb-2">
                            Sonuç bulunamadı
                        </h3>
                        <p class="text-gray-500 dark:text-gray-500 mb-4">
                            "<?php echo clean($query); ?>" için herhangi bir sonuç bulunamadı.
                        </p>
                        <div class="text-sm text-gray-500 dark:text-gray-500">
                            <p class="mb-2">Arama önerileri:</p>
                            <ul class="list-disc list-inside space-y-1">
                                <li>Farklı anahtar kelimeler deneyin</li>
                                <li>Daha genel terimler kullanın</li>
                                <li>Yazım hatalarını kontrol edin</li>
                            </ul>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="space-y-6">
                        <?php foreach ($results as $result): ?>
                            <article class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                                    <a href="post.php?slug=<?php echo clean($result['slug']); ?>" 
                                       class="hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                                        <?php echo clean($result['title']); ?>
                                    </a>
                                </h2>
                                
                                <div class="flex flex-wrap items-center text-sm text-gray-500 dark:text-gray-400 space-x-4 mb-3">
                                    <span class="flex items-center">
                                        <i class="fas fa-user mr-1"></i>
                                        <?php echo clean($result['author_name'] ?: $result['author_username']); ?>
                                    </span>
                                    <span class="flex items-center">
                                        <i class="fas fa-calendar mr-1"></i>
                                        <?php echo formatDate($result['created_at'], 'd.m.Y'); ?>
                                    </span>
                                    <span class="flex items-center">
                                        <i class="fas fa-eye mr-1"></i>
                                        <?php echo number_format($result['views']); ?> görüntülenme
                                    </span>
                                    <span class="flex items-center">
                                        <i class="fas fa-comments mr-1"></i>
                                        <?php echo $result['comment_count']; ?> yorum
                                    </span>
                                </div>
                                
                                <div class="text-gray-600 dark:text-gray-300 leading-relaxed mb-4">
                                    <?php 
                                    $excerpt_text = $result['excerpt'] ?: excerpt($result['content'], 200);
                                    // Arama terimini vurgula
                                    $highlighted = preg_replace('/(' . preg_quote($query, '/') . ')/i', '<mark class="bg-yellow-200 dark:bg-yellow-600">$1</mark>', $excerpt_text);
                                    echo $highlighted;
                                    ?>
                                </div>
                                
                                <a href="post.php?slug=<?php echo clean($result['slug']); ?>" 
                                   class="inline-flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium">
                                    Devamını Oku
                                    <i class="fas fa-arrow-right ml-2"></i>
                                </a>
                            </article>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($pagination['total_pages'] > 1): ?>
                        <div class="flex justify-center mt-8">
                            <nav class="flex space-x-2">
                                <?php if ($pagination['has_prev']): ?>
                                    <a href="?q=<?php echo urlencode($query); ?>&page=<?php echo $pagination['prev_page']; ?>" 
                                       class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700">
                                        Önceki
                                    </a>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                                    <?php if ($i == $pagination['current_page']): ?>
                                        <span class="px-3 py-2 text-sm font-medium text-white bg-primary-600 border border-primary-600 rounded-md">
                                            <?php echo $i; ?>
                                        </span>
                                    <?php else: ?>
                                        <a href="?q=<?php echo urlencode($query); ?>&page=<?php echo $i; ?>" 
                                           class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700">
                                            <?php echo $i; ?>
                                        </a>
                                    <?php endif; ?>
                                <?php endfor; ?>
                                
                                <?php if ($pagination['has_next']): ?>
                                    <a href="?q=<?php echo urlencode($query); ?>&page=<?php echo $pagination['next_page']; ?>" 
                                       class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700">
                                        Sonraki
                                    </a>
                                <?php endif; ?>
                            </nav>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
            
            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Popular Searches -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 pb-2 border-b-2 border-primary-600">
                        Popüler Aramalar
                    </h3>
                    <div class="space-y-2">
                        <a href="?q=teknoloji" class="block text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                            Teknoloji
                        </a>
                        <a href="?q=araçlar" class="block text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                            Araçlar
                        </a>
                        <a href="?q=youtube" class="block text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                            YouTube
                        </a>
                        <a href="?q=web" class="block text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                            Web
                        </a>
                    </div>
                </div>
                
                <!-- Recent Posts -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 pb-2 border-b-2 border-primary-600">
                        Son Yazılar
                    </h3>
                    <?php
                    try {
                        $recent_posts = $db->fetchAll("
                            SELECT title, slug, created_at 
                            FROM posts 
                            WHERE status = 'published' 
                            ORDER BY created_at DESC 
                            LIMIT 5
                        ");
                        
                        if (!empty($recent_posts)):
                    ?>
                        <div class="space-y-3">
                            <?php foreach ($recent_posts as $recent_post): ?>
                                <div>
                                    <h4 class="text-sm font-medium text-gray-900 dark:text-white leading-tight mb-1">
                                        <a href="post.php?slug=<?php echo clean($recent_post['slug']); ?>" 
                                           class="hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                                            <?php echo clean($recent_post['title']); ?>
                                        </a>
                                    </h4>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">
                                        <?php echo formatDate($recent_post['created_at'], 'd.m.Y'); ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php 
                        else:
                    ?>
                        <p class="text-gray-500 dark:text-gray-400 text-sm">Henüz yazı yok.</p>
                    <?php 
                        endif;
                    } catch (Exception $e) {
                        echo '<p class="text-gray-500 dark:text-gray-400 text-sm">Yazılar yüklenemedi.</p>';
                    }
                    ?>
                </div>
            </div>
        </div>
    <?php else: ?>
        <!-- Search Suggestions -->
        <div class="max-w-4xl mx-auto">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>Arama İpuçları
                    </h3>
                    <ul class="space-y-2 text-gray-600 dark:text-gray-300">
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mr-2 mt-1"></i>
                            En az 2 karakter girin
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mr-2 mt-1"></i>
                            Başlık ve içerikte arama yapar
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mr-2 mt-1"></i>
                            Türkçe karakterler desteklenir
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mr-2 mt-1"></i>
                            Büyük/küçük harf duyarlı değil
                        </li>
                    </ul>
                </div>
                
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        <i class="fas fa-tags text-blue-500 mr-2"></i>Popüler Konular
                    </h3>
                    <div class="flex flex-wrap gap-2">
                        <a href="?q=teknoloji" class="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-3 py-1 rounded-full text-sm hover:bg-primary-100 dark:hover:bg-primary-900 transition-colors">
                            Teknoloji
                        </a>
                        <a href="?q=araçlar" class="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-3 py-1 rounded-full text-sm hover:bg-primary-100 dark:hover:bg-primary-900 transition-colors">
                            Araçlar
                        </a>
                        <a href="?q=youtube" class="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-3 py-1 rounded-full text-sm hover:bg-primary-100 dark:hover:bg-primary-900 transition-colors">
                            YouTube
                        </a>
                        <a href="?q=web" class="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-3 py-1 rounded-full text-sm hover:bg-primary-100 dark:hover:bg-primary-900 transition-colors">
                            Web
                        </a>
                        <a href="?q=görsel" class="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-3 py-1 rounded-full text-sm hover:bg-primary-100 dark:hover:bg-primary-900 transition-colors">
                            Görsel
                        </a>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php include 'includes/footer.php'; ?>
