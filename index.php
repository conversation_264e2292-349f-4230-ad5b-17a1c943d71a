<?php
require_once 'config.php';

// Sayfa bilgileri
$page_title = 'Ana Sayfa';
$page_description = 'ToolHane Blog - Tek<PERSON><PERSON>ji, araçlar ve dijital dünya hakkında güncel içerikler';

// Veritabanı bağlantısı
$db = Database::getInstance();

// Sayfalama ayarları
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$limit = POSTS_PER_PAGE;
$offset = ($page - 1) * $limit;

try {
    // Toplam yazı sayısını al
    $total_posts = $db->count('posts', 'status = ?', ['published']);
    
    // Blog yazılarını al
    $posts_sql = "
        SELECT p.*, u.full_name as author_name, u.username as author_username,
               (SELECT COUNT(*) FROM comments c WHERE c.post_id = p.id AND c.status = 'approved') as comment_count
        FROM posts p 
        LEFT JOIN users u ON p.author_id = u.id 
        WHERE p.status = 'published' 
        ORDER BY p.created_at DESC 
        LIMIT ? OFFSET ?
    ";
    $posts = $db->fetchAll($posts_sql, [$limit, $offset]);
    
    // Popüler yazıları al (en çok görüntülenen)
    $popular_posts_sql = "
        SELECT p.*, u.full_name as author_name
        FROM posts p 
        LEFT JOIN users u ON p.author_id = u.id 
        WHERE p.status = 'published' 
        ORDER BY p.views DESC 
        LIMIT 5
    ";
    $popular_posts = $db->fetchAll($popular_posts_sql);
    
    // Son yorumları al
    $recent_comments_sql = "
        SELECT c.*, p.title as post_title, p.slug as post_slug
        FROM comments c 
        LEFT JOIN posts p ON c.post_id = p.id 
        WHERE c.status = 'approved' AND p.status = 'published'
        ORDER BY c.created_at DESC 
        LIMIT 5
    ";
    $recent_comments = $db->fetchAll($recent_comments_sql);
    
    // Kategorileri al
    $categories = $db->fetchAll("SELECT * FROM categories ORDER BY name");
    
    // Sayfalama hesapla
    $pagination = calculatePagination($page, $total_posts, $limit);
    
} catch (Exception $e) {
    error_log("Index page error: " . $e->getMessage());
    $posts = [];
    $popular_posts = [];
    $recent_comments = [];
    $categories = [];
    $pagination = ['total_pages' => 1, 'current_page' => 1];
}

// Header'ı dahil et
include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="bg-gradient-to-r from-primary-600 to-secondary-600 text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl md:text-6xl font-bold mb-6">
            ToolHane Blog
        </h1>
        <p class="text-xl md:text-2xl mb-8 text-primary-100">
            Teknoloji, araçlar ve dijital dünya hakkında güncel içerikler
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="#posts" class="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                Blog Yazılarını Keşfet
            </a>
            <a href="https://www.toolhane.com" target="_blank" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-colors">
                ToolHane Ana Site
            </a>
        </div>
    </div>
</section>

<!-- Main Content -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Blog Posts -->
        <div class="lg:col-span-2">
            <div id="posts" class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                    Son Blog Yazıları
                </h2>
                
                <?php if (empty($posts)): ?>
                    <div class="text-center py-12">
                        <i class="fas fa-file-alt text-6xl text-gray-300 dark:text-gray-600 mb-4"></i>
                        <h3 class="text-xl font-semibold text-gray-600 dark:text-gray-400 mb-2">
                            Henüz blog yazısı yok
                        </h3>
                        <p class="text-gray-500 dark:text-gray-500">
                            İlk blog yazısı için admin panelini kullanabilirsiniz.
                        </p>
                        <a href="admin/login.php" class="inline-block mt-4 bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors">
                            Admin Paneli
                        </a>
                    </div>
                <?php else: ?>
                    <div class="space-y-8">
                        <?php foreach ($posts as $post): ?>
                            <article class="border-b border-gray-200 dark:border-gray-700 pb-8 last:border-b-0">
                                <div class="flex flex-col md:flex-row gap-6">
                                    <!-- Featured Image -->
                                    <?php if (!empty($post['featured_image'])): ?>
                                        <div class="md:w-1/3 flex-shrink-0">
                                            <a href="post.php?slug=<?php echo clean($post['slug']); ?>" class="block">
                                                <img src="uploads/posts/medium/<?php echo date('Y/m', strtotime($post['created_at'])); ?>/<?php echo clean($post['featured_image']); ?>"
                                                     alt="<?php echo clean($post['title']); ?>"
                                                     class="w-full h-48 object-cover rounded-lg hover:opacity-90 transition-opacity"
                                                     loading="lazy">
                                            </a>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Content -->
                                    <div class="flex-1 space-y-4">
                                        <div>
                                            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                                                <a href="post.php?slug=<?php echo clean($post['slug']); ?>"
                                                   class="hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                                                    <?php echo clean($post['title']); ?>
                                                </a>
                                            </h3>
                                        
                                        <div class="flex flex-wrap items-center text-sm text-gray-500 dark:text-gray-400 space-x-4 mb-3">
                                            <span class="flex items-center">
                                                <i class="fas fa-user mr-1"></i>
                                                <?php echo clean($post['author_name'] ?: $post['author_username']); ?>
                                            </span>
                                            <span class="flex items-center">
                                                <i class="fas fa-calendar mr-1"></i>
                                                <?php echo formatDate($post['created_at'], 'd.m.Y'); ?>
                                            </span>
                                            <span class="flex items-center">
                                                <i class="fas fa-eye mr-1"></i>
                                                <?php echo number_format($post['views']); ?> görüntülenme
                                            </span>
                                            <span class="flex items-center">
                                                <i class="fas fa-comments mr-1"></i>
                                                <?php echo $post['comment_count']; ?> yorum
                                            </span>
                                        </div>
                                    </div>
                                    
                                    <div class="text-gray-600 dark:text-gray-300 leading-relaxed">
                                        <?php 
                                        $excerpt_text = $post['excerpt'] ?: excerpt($post['content'], 200);
                                        echo clean($excerpt_text);
                                        ?>
                                    </div>
                                    
                                    <div>
                                        <a href="post.php?slug=<?php echo clean($post['slug']); ?>" 
                                           class="inline-flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium">
                                            Devamını Oku
                                            <i class="fas fa-arrow-right ml-2"></i>
                                        </a>
                                    </div>
                                </div>
                            </article>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($pagination['total_pages'] > 1): ?>
                        <div class="flex justify-center mt-8">
                            <nav class="flex space-x-2">
                                <?php if ($pagination['has_prev']): ?>
                                    <a href="?page=<?php echo $pagination['prev_page']; ?>" 
                                       class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700">
                                        Önceki
                                    </a>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                                    <?php if ($i == $pagination['current_page']): ?>
                                        <span class="px-3 py-2 text-sm font-medium text-white bg-primary-600 border border-primary-600 rounded-md">
                                            <?php echo $i; ?>
                                        </span>
                                    <?php else: ?>
                                        <a href="?page=<?php echo $i; ?>" 
                                           class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700">
                                            <?php echo $i; ?>
                                        </a>
                                    <?php endif; ?>
                                <?php endfor; ?>
                                
                                <?php if ($pagination['has_next']): ?>
                                    <a href="?page=<?php echo $pagination['next_page']; ?>" 
                                       class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700">
                                        Sonraki
                                    </a>
                                <?php endif; ?>
                            </nav>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Popüler Yazılar -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 pb-2 border-b-2 border-primary-600">
                    Popüler Yazılar
                </h3>
                
                <?php if (empty($popular_posts)): ?>
                    <p class="text-gray-500 dark:text-gray-400 text-sm">Henüz popüler yazı yok.</p>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php foreach ($popular_posts as $popular_post): ?>
                            <div class="flex space-x-3">
                                <div class="flex-1">
                                    <h4 class="text-sm font-medium text-gray-900 dark:text-white leading-tight mb-1">
                                        <a href="post.php?slug=<?php echo clean($popular_post['slug']); ?>" 
                                           class="hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                                            <?php echo clean($popular_post['title']); ?>
                                        </a>
                                    </h4>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">
                                        <?php echo formatDate($popular_post['created_at'], 'd.m.Y'); ?> • 
                                        <?php echo number_format($popular_post['views']); ?> görüntülenme
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- Kategoriler -->
            <?php if (!empty($categories)): ?>
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 pb-2 border-b-2 border-primary-600">
                        Kategoriler
                    </h3>
                    <div class="space-y-2">
                        <?php foreach ($categories as $category): ?>
                            <a href="category.php?slug=<?php echo clean($category['slug']); ?>" 
                               class="block text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                                <?php echo clean($category['name']); ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Son Yorumlar -->
            <?php if (!empty($recent_comments)): ?>
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 pb-2 border-b-2 border-primary-600">
                        Son Yorumlar
                    </h3>
                    <div class="space-y-4">
                        <?php foreach ($recent_comments as $comment): ?>
                            <div class="text-sm">
                                <div class="font-medium text-gray-900 dark:text-white mb-1">
                                    <?php echo clean($comment['author_name']); ?>
                                </div>
                                <div class="text-gray-600 dark:text-gray-300 mb-2">
                                    <?php echo clean(excerpt($comment['content'], 80)); ?>
                                </div>
                                <a href="post.php?slug=<?php echo clean($comment['post_slug']); ?>#comment-<?php echo $comment['id']; ?>" 
                                   class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300">
                                    <?php echo clean($comment['post_title']); ?>
                                </a>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
