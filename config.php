<?php
/**
 * ToolHane Blog Konfigürasyon Dosyası
 */

// Hata raporlama ayarları
error_reporting(E_ALL);
ini_set('display_errors', 0); // Üretim ortamında 0 olmalı

// Veritabanı ayarları
define('DB_HOST', 'localhost');
define('DB_NAME', 'toolhane_blog');
define('DB_USER', 'toolhane');
define('DB_PASS', '85f2sQv9kF');
define('DB_CHARSET', 'utf8mb4');

// Site ayarları
define('SITE_NAME', 'ToolHane Blog');
define('SITE_URL', 'https://blog.toolhane.com');
define('SITE_DESCRIPTION', 'ToolHane resmi blog sitesi');
define('ADMIN_EMAIL', '<EMAIL>');

// Güvenlik ayarları
define('SESSION_NAME', 'toolhane_blog_session');
define('CSRF_TOKEN_NAME', 'csrf_token');

// <PERSON><PERSON><PERSON>a ayarlar<PERSON>
define('POSTS_PER_PAGE', 10);
define('COMMENTS_PER_PAGE', 20);

// Dosya yükleme ayarları
define('UPLOAD_DIR', 'assets/images/uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);

// Timezone ayarı
date_default_timezone_set('Europe/Istanbul');

// Session ayarları
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 0); // HTTPS kullanıyorsanız 1 yapın

// Session başlat
if (session_status() === PHP_SESSION_NONE) {
    session_name(SESSION_NAME);
    session_start();
}

// Autoload fonksiyonu
spl_autoload_register(function ($class) {
    $file = __DIR__ . '/includes/' . $class . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
});

// Yardımcı fonksiyonları yükle
require_once __DIR__ . '/includes/functions.php';
