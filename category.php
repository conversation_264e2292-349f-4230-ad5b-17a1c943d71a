<?php
require_once 'config.php';

// Kategori slug'ını al
$category_slug = isset($_GET['slug']) ? clean($_GET['slug']) : '';

if (empty($category_slug)) {
    header('Location: index.php');
    exit;
}

// Veritabanı bağlantısı
$db = Database::getInstance();

try {
    // Kategoriyi al
    $category = $db->fetch("SELECT * FROM categories WHERE slug = ?", [$category_slug]);
    
    if (!$category) {
        header('HTTP/1.0 404 Not Found');
        include '404.php';
        exit;
    }
    
    // Sayfalama
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $limit = POSTS_PER_PAGE;
    $offset = ($page - 1) * $limit;
    
    // Kategorideki toplam yazı sayısını al
    $total_posts = $db->count('posts p', 'p.status = ? AND EXISTS (SELECT 1 FROM post_categories pc WHERE pc.post_id = p.id AND pc.category_id = ?)', ['published', $category['id']]);
    
    // Kategorideki yazıları al
    $posts_sql = "
        SELECT p.*, u.full_name as author_name, u.username as author_username,
               (SELECT COUNT(*) FROM comments c WHERE c.post_id = p.id AND c.status = 'approved') as comment_count
        FROM posts p 
        LEFT JOIN users u ON p.author_id = u.id 
        INNER JOIN post_categories pc ON p.id = pc.post_id
        WHERE p.status = 'published' AND pc.category_id = ?
        ORDER BY p.created_at DESC 
        LIMIT ? OFFSET ?
    ";
    $posts = $db->fetchAll($posts_sql, [$category['id'], $limit, $offset]);
    
    // Sayfalama hesapla
    $pagination = calculatePagination($page, $total_posts, $limit);
    
    // Diğer kategorileri al
    $other_categories = $db->fetchAll("SELECT * FROM categories WHERE id != ? ORDER BY name", [$category['id']]);
    
} catch (Exception $e) {
    error_log("Category page error: " . $e->getMessage());
    header('HTTP/1.0 500 Internal Server Error');
    include '500.php';
    exit;
}

// Sayfa bilgileri
$page_title = $category['name'];
$page_description = $category['description'] ?: $category['name'] . ' kategorisindeki blog yazıları';

// Breadcrumb
$breadcrumb_items = [
    ['title' => 'Ana Sayfa', 'url' => 'index.php'],
    ['title' => 'Kategoriler', 'url' => '#'],
    ['title' => $category['name'], 'url' => '']
];

// Header'ı dahil et
include 'includes/header.php';
?>

<!-- Breadcrumb -->
<div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <?php echo getBreadcrumb($breadcrumb_items); ?>
    </div>
</div>

<!-- Category Header -->
<div class="bg-gradient-to-r from-primary-600 to-secondary-600 text-white py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-3xl md:text-4xl font-bold mb-4">
            <?php echo clean($category['name']); ?>
        </h1>
        <?php if (!empty($category['description'])): ?>
            <p class="text-xl text-primary-100 mb-4">
                <?php echo clean($category['description']); ?>
            </p>
        <?php endif; ?>
        <p class="text-primary-200">
            <?php echo number_format($total_posts); ?> yazı
        </p>
    </div>
</div>

<!-- Main Content -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Posts -->
        <div class="lg:col-span-2">
            <?php if (empty($posts)): ?>
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-8 text-center">
                    <i class="fas fa-file-alt text-4xl text-gray-300 dark:text-gray-600 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-600 dark:text-gray-400 mb-2">
                        Bu kategoride henüz yazı yok
                    </h3>
                    <p class="text-gray-500 dark:text-gray-500 mb-4">
                        <?php echo clean($category['name']); ?> kategorisinde henüz yayınlanmış yazı bulunmuyor.
                    </p>
                    <a href="index.php" class="inline-block bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors">
                        Tüm Yazıları Görüntüle
                    </a>
                </div>
            <?php else: ?>
                <div class="space-y-8">
                    <?php foreach ($posts as $post): ?>
                        <article class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                                <a href="post.php?slug=<?php echo clean($post['slug']); ?>" 
                                   class="hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                                    <?php echo clean($post['title']); ?>
                                </a>
                            </h2>
                            
                            <div class="flex flex-wrap items-center text-sm text-gray-500 dark:text-gray-400 space-x-4 mb-3">
                                <span class="flex items-center">
                                    <i class="fas fa-user mr-1"></i>
                                    <?php echo clean($post['author_name'] ?: $post['author_username']); ?>
                                </span>
                                <span class="flex items-center">
                                    <i class="fas fa-calendar mr-1"></i>
                                    <?php echo formatDate($post['created_at'], 'd.m.Y'); ?>
                                </span>
                                <span class="flex items-center">
                                    <i class="fas fa-eye mr-1"></i>
                                    <?php echo number_format($post['views']); ?> görüntülenme
                                </span>
                                <span class="flex items-center">
                                    <i class="fas fa-comments mr-1"></i>
                                    <?php echo $post['comment_count']; ?> yorum
                                </span>
                            </div>
                            
                            <div class="text-gray-600 dark:text-gray-300 leading-relaxed mb-4">
                                <?php 
                                $excerpt_text = $post['excerpt'] ?: excerpt($post['content'], 200);
                                echo clean($excerpt_text);
                                ?>
                            </div>
                            
                            <a href="post.php?slug=<?php echo clean($post['slug']); ?>" 
                               class="inline-flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium">
                                Devamını Oku
                                <i class="fas fa-arrow-right ml-2"></i>
                            </a>
                        </article>
                    <?php endforeach; ?>
                </div>
                
                <!-- Pagination -->
                <?php if ($pagination['total_pages'] > 1): ?>
                    <div class="flex justify-center mt-8">
                        <nav class="flex space-x-2">
                            <?php if ($pagination['has_prev']): ?>
                                <a href="?slug=<?php echo urlencode($category_slug); ?>&page=<?php echo $pagination['prev_page']; ?>" 
                                   class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700">
                                    Önceki
                                </a>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                                <?php if ($i == $pagination['current_page']): ?>
                                    <span class="px-3 py-2 text-sm font-medium text-white bg-primary-600 border border-primary-600 rounded-md">
                                        <?php echo $i; ?>
                                    </span>
                                <?php else: ?>
                                    <a href="?slug=<?php echo urlencode($category_slug); ?>&page=<?php echo $i; ?>" 
                                       class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700">
                                        <?php echo $i; ?>
                                    </a>
                                <?php endif; ?>
                            <?php endfor; ?>
                            
                            <?php if ($pagination['has_next']): ?>
                                <a href="?slug=<?php echo urlencode($category_slug); ?>&page=<?php echo $pagination['next_page']; ?>" 
                                   class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700">
                                    Sonraki
                                </a>
                            <?php endif; ?>
                        </nav>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
        
        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Other Categories -->
            <?php if (!empty($other_categories)): ?>
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 pb-2 border-b-2 border-primary-600">
                        Diğer Kategoriler
                    </h3>
                    <div class="space-y-2">
                        <?php foreach ($other_categories as $other_category): ?>
                            <a href="category.php?slug=<?php echo clean($other_category['slug']); ?>" 
                               class="block text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                                <?php echo clean($other_category['name']); ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Popular Posts -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 pb-2 border-b-2 border-primary-600">
                    Popüler Yazılar
                </h3>
                <?php
                try {
                    $popular_posts = $db->fetchAll("
                        SELECT p.*, u.full_name as author_name
                        FROM posts p 
                        LEFT JOIN users u ON p.author_id = u.id 
                        WHERE p.status = 'published' 
                        ORDER BY p.views DESC 
                        LIMIT 5
                    ");
                    
                    if (!empty($popular_posts)):
                ?>
                    <div class="space-y-4">
                        <?php foreach ($popular_posts as $popular_post): ?>
                            <div class="flex space-x-3">
                                <div class="flex-1">
                                    <h4 class="text-sm font-medium text-gray-900 dark:text-white leading-tight mb-1">
                                        <a href="post.php?slug=<?php echo clean($popular_post['slug']); ?>" 
                                           class="hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                                            <?php echo clean($popular_post['title']); ?>
                                        </a>
                                    </h4>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">
                                        <?php echo formatDate($popular_post['created_at'], 'd.m.Y'); ?> • 
                                        <?php echo number_format($popular_post['views']); ?> görüntülenme
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php 
                    else:
                ?>
                    <p class="text-gray-500 dark:text-gray-400 text-sm">Henüz popüler yazı yok.</p>
                <?php 
                    endif;
                } catch (Exception $e) {
                    echo '<p class="text-gray-500 dark:text-gray-400 text-sm">Yazılar yüklenemedi.</p>';
                }
                ?>
            </div>
            
            <!-- Back to Home -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 text-center">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    Daha Fazla İçerik
                </h3>
                <p class="text-gray-600 dark:text-gray-300 mb-4">
                    Tüm blog yazılarımızı keşfetmek için ana sayfayı ziyaret edin.
                </p>
                <a href="index.php" 
                   class="inline-block bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors">
                    Ana Sayfa
                </a>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
