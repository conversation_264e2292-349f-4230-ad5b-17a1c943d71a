<?php
require_once '../config.php';
require_once '../includes/ImageUploader.php';

// Post ID'yi al
$post_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($post_id <= 0) {
    setFlashMessage('error', 'Geçersiz yazı ID\'si.');
    header('Location: posts.php');
    exit;
}

// Veritabanı bağlantısı
$db = Database::getInstance();

try {
    // Yazıyı al
    $post = $db->fetch("
        SELECT p.*, u.full_name as author_name 
        FROM posts p 
        LEFT JOIN users u ON p.author_id = u.id 
        WHERE p.id = ?
    ", [$post_id]);
    
    if (!$post) {
        setFlashMessage('error', 'Yazı bulunamadı.');
        header('Location: posts.php');
        exit;
    }
    
    // Yazının kategorilerini al
    $post_categories = $db->fetchAll("
        SELECT category_id 
        FROM post_categories 
        WHERE post_id = ?
    ", [$post_id]);
    
    $selected_categories = array_column($post_categories, 'category_id');

    // Postun öne çıkan görselini post_images tablosundan al
    $main_image = $db->fetch("SELECT * FROM post_images WHERE post_id = ? AND is_featured = 1 ORDER BY id ASC LIMIT 1", [$post_id]);
    
} catch (Exception $e) {
    error_log("Post edit fetch error: " . $e->getMessage());
    setFlashMessage('error', 'Yazı yüklenirken hata oluştu.');
    header('Location: posts.php');
    exit;
}

// Sayfa bilgileri
$page_title = 'Yazı Düzenle: ' . $post['title'];
$page_description = 'Blog yazısını düzenle';

// Form gönderildi mi?
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = clean($_POST['title'] ?? '');
    $slug = clean($_POST['slug'] ?? '');
    $excerpt = clean($_POST['excerpt'] ?? '');
    $content = $_POST['content'] ?? ''; // HTML içerik olduğu için clean() kullanmıyoruz
    $status = clean($_POST['status'] ?? 'draft');
    $meta_title = clean($_POST['meta_title'] ?? '');
    $meta_description = clean($_POST['meta_description'] ?? '');
    $categories = $_POST['categories'] ?? [];
    $featured_image = clean($_POST['featured_image'] ?? '');
    
    $errors = [];
    
    // Validasyon
    if (empty($title)) {
        $errors[] = 'Başlık alanı zorunludur.';
    }
    
    if (empty($content)) {
        $errors[] = 'İçerik alanı zorunludur.';
    }
    
    // Slug kontrol et
    if (empty($slug)) {
        $slug = createSlug($title);
    } else {
        $slug = createSlug($slug);
    }
    
    // Slug benzersizlik kontrolü (mevcut yazı hariç)
    if (!empty($slug)) {
        $existing_slug = $db->fetch("SELECT id FROM posts WHERE slug = ? AND id != ?", [$slug, $post_id]);
        if ($existing_slug) {
            $slug = $slug . '-' . time();
        }
    }
    
    if (empty($slug)) {
        $errors[] = 'Geçerli bir slug oluşturulamadı.';
    }
    
    // CSRF token kontrolü
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Güvenlik hatası. Lütfen tekrar deneyin.';
    }
    
    if (empty($errors)) {
        try {
            $db->beginTransaction();
            
            // Yazıyı güncelle
            $post_data = [
                'title' => $title,
                'slug' => $slug,
                'excerpt' => $excerpt,
                'content' => $content,
                'status' => $status,
                'meta_title' => $meta_title,
                'meta_description' => $meta_description,
                'featured_image' => $featured_image
            ];
            
            $db->update('posts', $post_data, 'id = ?', [$post_id]);
            
            // Mevcut kategorileri sil
            $db->delete('post_categories', 'post_id = ?', [$post_id]);
            
            // Yeni kategorileri ekle
            if (!empty($categories)) {
                foreach ($categories as $category_id) {
                    $category_id = intval($category_id);
                    if ($category_id > 0) {
                        $db->insert('post_categories', [
                            'post_id' => $post_id,
                            'category_id' => $category_id
                        ]);
                    }
                }
            }
            
            $db->commit();
            
            // Post verilerini güncelle
            $post = array_merge($post, $post_data);
            $selected_categories = array_map('intval', $categories);
            
            setFlashMessage('success', 'Yazı başarıyla güncellendi.');
            
        } catch (Exception $e) {
            $db->rollback();
            error_log("Post edit error: " . $e->getMessage());
            setFlashMessage('error', 'Yazı güncellenirken hata oluştu: ' . $e->getMessage());
        }
    } else {
        foreach ($errors as $error) {
            setFlashMessage('error', $error);
        }
    }
}

// Kategorileri al
try {
    $categories = $db->fetchAll("SELECT * FROM categories ORDER BY name");
} catch (Exception $e) {
    $categories = [];
}

// Header'ı dahil et
include 'includes/header.php';
?>

<!-- Post Edit Form -->
<div class="max-w-4xl mx-auto">
    <!-- Header Actions -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Yazı Düzenle</h1>
            <p class="text-gray-600">Son güncelleme: <?php echo formatDate($post['updated_at'], 'd.m.Y H:i'); ?></p>
        </div>
        <div class="flex space-x-2">
            <a href="../post.php?slug=<?php echo clean($post['slug']); ?>" target="_blank" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                <i class="fas fa-external-link-alt mr-2"></i>Görüntüle
            </a>
            <a href="posts.php" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Geri Dön
            </a>
        </div>
    </div>
    
    <form method="POST" id="post-form" class="space-y-6">
        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
        
        <!-- Main Content -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Left Column - Main Content -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Title -->
                    <div>
                        <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                            Başlık *
                        </label>
                        <input type="text" 
                               id="title" 
                               name="title" 
                               required
                               value="<?php echo clean($post['title']); ?>"
                               onkeyup="generateSlug(this.value, 'slug')"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-lg">
                    </div>
                    
                    <!-- Slug -->
                    <div>
                        <label for="slug" class="block text-sm font-medium text-gray-700 mb-2">
                            URL Slug
                        </label>
                        <div class="flex">
                            <span class="inline-flex items-center px-3 rounded-l-lg border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                                <?php echo SITE_URL; ?>/post.php?slug=
                            </span>
                            <input type="text" 
                                   id="slug" 
                                   name="slug"
                                   value="<?php echo clean($post['slug']); ?>"
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-r-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                        </div>
                    </div>
                    
                    <!-- Excerpt -->
                    <div>
                        <label for="excerpt" class="block text-sm font-medium text-gray-700 mb-2">
                            Özet
                        </label>
                        <textarea id="excerpt" 
                                  name="excerpt" 
                                  rows="3"
                                  maxlength="300"
                                  onkeyup="updateCharCount(this, 'excerpt-count', 300)"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                                  placeholder="Yazının kısa özeti..."><?php echo clean($post['excerpt']); ?></textarea>
                        <div class="flex justify-between text-xs text-gray-500 mt-1">
                            <span>Ana sayfada ve arama sonuçlarında gösterilir</span>
                            <span id="excerpt-count">0/300</span>
                        </div>
                    </div>
                    
                    <!-- Content -->
                    <div>
                        <label for="content" class="block text-sm font-medium text-gray-700 mb-2">
                            İçerik *
                        </label>
                        <textarea id="content" 
                                  name="content" 
                                  class="tinymce-editor"
                                  required><?php echo htmlspecialchars($post['content']); ?></textarea>
                    </div>
                </div>
                
                <!-- Right Column - Sidebar -->
                <div class="space-y-6">
                    <!-- Post Info -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Yazı Bilgileri</h3>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Yazar:</span>
                                <span class="font-medium"><?php echo clean($post['author_name']); ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Oluşturulma:</span>
                                <span><?php echo formatDate($post['created_at'], 'd.m.Y H:i'); ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Görüntülenme:</span>
                                <span><?php echo number_format($post['views']); ?></span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Publish Box -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Yayınlama</h3>
                        
                        <!-- Featured Image -->
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Öne Çıkan Fotoğraf</label>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-primary-400 transition-colors">
                                <?php if (!empty($main_image) && !empty($main_image['file_path'])): ?>
                                    <div id="featured-image-preview" class="">
                                        <img id="featured-image-img" src="/<?php echo clean($main_image['file_path']); ?>" alt="Preview" class="max-w-full h-32 object-cover rounded-lg mx-auto mb-2">
                                        <p id="featured-image-name" class="text-sm text-gray-600 mb-2"><?php echo clean($main_image['filename']); ?></p>
                                        <button type="button" onclick="removeFeaturedImage()" class="text-red-600 hover:text-red-800 text-sm">
                                            <i class="fas fa-trash mr-1"></i>Kaldır
                                        </button>
                                    </div>
                                <?php else: ?>
                                    <div id="featured-image-preview" class="hidden">
                                        <img id="featured-image-img" src="" alt="Preview" class="max-w-full h-32 object-cover rounded-lg mx-auto mb-2">
                                        <p id="featured-image-name" class="text-sm text-gray-600 mb-2"></p>
                                        <button type="button" onclick="removeFeaturedImage()" class="text-red-600 hover:text-red-800 text-sm">
                                            <i class="fas fa-trash mr-1"></i>Kaldır
                                        </button>
                                    </div>
                                <?php endif; ?>
                                <div id="featured-image-upload" class="<?php echo (!empty($main_image) && !empty($main_image['file_path'])) ? 'hidden' : ''; ?>">
                                    <i class="fas fa-cloud-upload-alt text-3xl text-gray-400 mb-2"></i>
                                    <p class="text-gray-600 mb-2">Fotoğraf yüklemek için tıklayın veya sürükleyin</p>
                                    <input type="file" id="featured-image-input" accept="image/*" class="hidden">
                                    <button type="button" onclick="document.getElementById('featured-image-input').click()" class="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors">
                                        Fotoğraf Seç
                                    </button>
                                </div>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">Maksimum 5MB, JPG, PNG, WebP formatları desteklenir</p>
                        </div>

                        <!-- Status -->
                        <div class="mb-4">
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Durum</label>
                            <select id="status" name="status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                                <option value="draft" <?php echo $post['status'] === 'draft' ? 'selected' : ''; ?>>Taslak</option>
                                <option value="published" <?php echo $post['status'] === 'published' ? 'selected' : ''; ?>>Yayınla</option>
                            </select>
                        </div>
                        
                        <!-- Actions -->
                        <div class="flex flex-col space-y-2">
                            <button type="submit" class="w-full bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 transition-colors">
                                <i class="fas fa-save mr-2"></i>Güncelle
                            </button>
                            <a href="posts.php" class="w-full bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors text-center">
                                <i class="fas fa-arrow-left mr-2"></i>Geri Dön
                            </a>
                            <a href="post-delete.php?id=<?php echo $post['id']; ?>" onclick="return confirm('Bu yazıyı silmek istediğinizden emin misiniz?')" class="w-full bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors text-center">
                                <i class="fas fa-trash mr-2"></i>Sil
                            </a>
                        </div>
                    </div>
                    
                    <!-- Categories -->
                    <?php if (!empty($categories)): ?>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Kategoriler</h3>
                            <div class="space-y-2 max-h-48 overflow-y-auto">
                                <?php foreach ($categories as $category): ?>
                                    <label class="flex items-center">
                                        <input type="checkbox" 
                                               name="categories[]" 
                                               value="<?php echo $category['id']; ?>"
                                               <?php echo in_array($category['id'], $selected_categories) ? 'checked' : ''; ?>
                                               class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                        <span class="ml-2 text-sm text-gray-700"><?php echo clean($category['name']); ?></span>
                                    </label>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <!-- SEO Settings -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">SEO Ayarları</h3>
                        
                        <!-- Meta Title -->
                        <div class="mb-4">
                            <label for="meta_title" class="block text-sm font-medium text-gray-700 mb-2">Meta Başlık</label>
                            <input type="text" 
                                   id="meta_title" 
                                   name="meta_title"
                                   maxlength="60"
                                   onkeyup="updateCharCount(this, 'meta-title-count', 60)"
                                   value="<?php echo clean($post['meta_title']); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                            <div class="flex justify-between text-xs text-gray-500 mt-1">
                                <span>Arama motorlarında görünecek başlık</span>
                                <span id="meta-title-count">0/60</span>
                            </div>
                        </div>
                        
                        <!-- Meta Description -->
                        <div>
                            <label for="meta_description" class="block text-sm font-medium text-gray-700 mb-2">Meta Açıklama</label>
                            <textarea id="meta_description" 
                                      name="meta_description" 
                                      rows="3"
                                      maxlength="160"
                                      onkeyup="updateCharCount(this, 'meta-desc-count', 160)"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                                      placeholder="Arama motorlarında görünecek açıklama..."><?php echo clean($post['meta_description']); ?></textarea>
                            <div class="flex justify-between text-xs text-gray-500 mt-1">
                                <span>Arama sonuçlarında görünecek açıklama</span>
                                <span id="meta-desc-count">0/160</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<?php 
$additional_scripts = '
<script>
// Initialize character counters on page load
document.addEventListener("DOMContentLoaded", function() {
    updateCharCount(document.getElementById("excerpt"), "excerpt-count", 300);
    updateCharCount(document.getElementById("meta_title"), "meta-title-count", 60);
    updateCharCount(document.getElementById("meta_description"), "meta-desc-count", 160);
    
    // Auto-save functionality
    enableAutoSave("post-form", 60000); // Auto-save every minute

    // Featured image upload
    initFeaturedImageUpload();
});

// Form validation
document.getElementById("post-form").addEventListener("submit", function(e) {
    const title = document.getElementById("title").value.trim();
    const content = tinymce.get("content").getContent();
    
    if (!title) {
        e.preventDefault();
        alert("Başlık alanı zorunludur.");
        document.getElementById("title").focus();
        return false;
    }
    
    if (!content || content.trim() === "") {
        e.preventDefault();
        alert("İçerik alanı zorunludur.");
        tinymce.get("content").focus();
        return false;
    }
    
    // Disable submit button to prevent double submission
    const submitBtn = this.querySelector("button[type=submit]");
    submitBtn.disabled = true;
    submitBtn.innerHTML = "<i class=\"fas fa-spinner fa-spin mr-2\"></i>Güncelleniyor...";
    
    return true;
});

// Featured Image Upload Functions (same as post-add.php)
function initFeaturedImageUpload() {
    const input = document.getElementById("featured-image-input");
    const dropzone = document.getElementById("featured-image-dropzone");

    // File input change
    input.addEventListener("change", function(e) {
        if (e.target.files.length > 0) {
            uploadFeaturedImage(e.target.files[0]);
        }
    });

    // Drag and drop
    dropzone.addEventListener("dragover", function(e) {
        e.preventDefault();
        dropzone.classList.add("border-primary-400", "bg-primary-50");
    });

    dropzone.addEventListener("dragleave", function(e) {
        e.preventDefault();
        dropzone.classList.remove("border-primary-400", "bg-primary-50");
    });

    dropzone.addEventListener("drop", function(e) {
        e.preventDefault();
        dropzone.classList.remove("border-primary-400", "bg-primary-50");

        if (e.dataTransfer.files.length > 0) {
            uploadFeaturedImage(e.dataTransfer.files[0]);
        }
    });
}

function uploadFeaturedImage(file) {
    // File validation
    if (!file.type.startsWith("image/")) {
        alert("Lütfen sadece resim dosyası seçin.");
        return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB
        alert("Dosya boyutu 5MB\'dan büyük olamaz.");
        return;
    }

    const formData = new FormData();
    formData.append("image", file);
    formData.append("alt_text", "");
    formData.append("caption", "");
    formData.append("post_id",

    // Show loading
    const uploadDiv = document.getElementById("featured-image-upload");
    const originalContent = uploadDiv.innerHTML;
    uploadDiv.innerHTML = `
        <div class="flex items-center justify-center">
            <i class="fas fa-spinner fa-spin text-2xl text-primary-600 mr-2"></i>
            <span class="text-primary-600">Yükleniyor...</span>
        </div>
    `;

    fetch("upload-image.php", {
        method: "POST",
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showFeaturedImagePreview(data.thumbnail, file.name, data.filename);
                if (document.getElementById("featured_image")) {
                    document.getElementById("featured_image").value = data.filename;
                }
        } else {
            alert("Yükleme hatası: " + data.error);
            uploadDiv.innerHTML = originalContent;
        }
    })
    .catch(error => {
        console.error("Upload error:", error);
        alert("Yükleme sırasında hata oluştu.");
        uploadDiv.innerHTML = originalContent;
    });
}

function showFeaturedImagePreview(imageSrc, fileName, imageValue) {
    document.getElementById("featured-image-img").src = imageSrc;
    document.getElementById("featured-image-name").textContent = fileName;
    document.getElementById("featured-image-preview").classList.remove("hidden");
    document.getElementById("featured-image-upload").classList.add("hidden");
}

function removeFeaturedImage() {
    document.getElementById("featured-image-preview").classList.add("hidden");
    document.getElementById("featured-image-upload").classList.remove("hidden");
    document.getElementById("featured_image").value = "";
    document.getElementById("featured-image-input").value = "";
}
</script>
';

include 'includes/footer.php'; 
?>
