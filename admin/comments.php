<?php
require_once '../config.php';

// Sayfa bilgileri
$page_title = 'Yorum Yönetimi';
$page_description = 'Blog yorumlarını görüntüle ve yönet';

// Veritabanı bağlantısı
$db = Database::getInstance();

// Bulk actions işlemi
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['bulk_action'])) {
    $action = clean($_POST['bulk_action']);
    $selected_ids = $_POST['selected'] ?? [];
    
    if (!empty($selected_ids) && !empty($action)) {
        try {
            foreach ($selected_ids as $id) {
                $id = intval($id);
                
                switch ($action) {
                    case 'approve':
                        $db->update('comments', ['status' => 'approved'], 'id = ?', [$id]);
                        break;
                    case 'spam':
                        $db->update('comments', ['status' => 'spam'], 'id = ?', [$id]);
                        break;
                    case 'pending':
                        $db->update('comments', ['status' => 'pending'], 'id = ?', [$id]);
                        break;
                    case 'delete':
                        $db->delete('comments', 'id = ?', [$id]);
                        break;
                }
            }
            
            $action_text = [
                'approve' => 'onaylandı',
                'spam' => 'spam olarak işaretlendi',
                'pending' => 'beklemede olarak işaretlendi',
                'delete' => 'silindi'
            ];
            
            setFlashMessage('success', count($selected_ids) . ' yorum ' . $action_text[$action] . '.');
        } catch (Exception $e) {
            setFlashMessage('error', 'İşlem sırasında hata oluştu: ' . $e->getMessage());
        }
    }
    
    header('Location: comments.php');
    exit;
}

// Tekil yorum işlemleri
if (isset($_GET['action']) && isset($_GET['id'])) {
    $action = clean($_GET['action']);
    $comment_id = intval($_GET['id']);
    
    if ($comment_id > 0) {
        try {
            switch ($action) {
                case 'approve':
                    $db->update('comments', ['status' => 'approved'], 'id = ?', [$comment_id]);
                    setFlashMessage('success', 'Yorum onaylandı.');
                    break;
                case 'spam':
                    $db->update('comments', ['status' => 'spam'], 'id = ?', [$comment_id]);
                    setFlashMessage('success', 'Yorum spam olarak işaretlendi.');
                    break;
                case 'pending':
                    $db->update('comments', ['status' => 'pending'], 'id = ?', [$comment_id]);
                    setFlashMessage('success', 'Yorum beklemede olarak işaretlendi.');
                    break;
                case 'delete':
                    $db->delete('comments', 'id = ?', [$comment_id]);
                    setFlashMessage('success', 'Yorum silindi.');
                    break;
            }
        } catch (Exception $e) {
            setFlashMessage('error', 'İşlem sırasında hata oluştu: ' . $e->getMessage());
        }
    }
    
    header('Location: comments.php');
    exit;
}

// Filtreleme ve sıralama
$status_filter = isset($_GET['status']) ? clean($_GET['status']) : '';
$search = isset($_GET['search']) ? clean($_GET['search']) : '';
$post_filter = isset($_GET['post']) ? intval($_GET['post']) : 0;

// Sayfalama
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

try {
    // WHERE koşulları oluştur
    $where_conditions = [];
    $params = [];
    
    if (!empty($status_filter)) {
        $where_conditions[] = 'c.status = ?';
        $params[] = $status_filter;
    }
    
    if (!empty($search)) {
        $where_conditions[] = '(c.author_name LIKE ? OR c.author_email LIKE ? OR c.content LIKE ?)';
        $params[] = '%' . $search . '%';
        $params[] = '%' . $search . '%';
        $params[] = '%' . $search . '%';
    }
    
    if ($post_filter > 0) {
        $where_conditions[] = 'c.post_id = ?';
        $params[] = $post_filter;
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // Toplam yorum sayısını al
    $count_sql = "SELECT COUNT(*) FROM comments c $where_clause";
    $total_comments = $db->fetch($count_sql, $params)['COUNT(*)'];
    
    // Yorumları al
    $comments_sql = "
        SELECT c.*, p.title as post_title, p.slug as post_slug
        FROM comments c 
        LEFT JOIN posts p ON c.post_id = p.id 
        $where_clause
        ORDER BY c.created_at DESC
        LIMIT ? OFFSET ?
    ";
    
    $comments_params = array_merge($params, [$limit, $offset]);
    $comments = $db->fetchAll($comments_sql, $comments_params);
    
    // Yazıları al (filtre için)
    $posts = $db->fetchAll("SELECT id, title FROM posts ORDER BY title");
    
    // İstatistikleri al
    $stats = [
        'total' => $db->count('comments'),
        'pending' => $db->count('comments', 'status = ?', ['pending']),
        'approved' => $db->count('comments', 'status = ?', ['approved']),
        'spam' => $db->count('comments', 'status = ?', ['spam'])
    ];
    
    // Sayfalama hesapla
    $pagination = calculatePagination($page, $total_comments, $limit);
    
} catch (Exception $e) {
    error_log("Comments page error: " . $e->getMessage());
    $comments = [];
    $posts = [];
    $stats = ['total' => 0, 'pending' => 0, 'approved' => 0, 'spam' => 0];
    $pagination = ['total_pages' => 1, 'current_page' => 1];
    setFlashMessage('error', 'Yorumlar yüklenirken hata oluştu.');
}

// Header'ı dahil et
include 'includes/header.php';
?>

<!-- Comments Management -->
<div class="space-y-6">
    <!-- Header with Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-white rounded-lg shadow p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-comments text-white"></i>
                    </div>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Toplam</p>
                    <p class="text-lg font-semibold text-gray-900"><?php echo number_format($stats['total']); ?></p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-clock text-white"></i>
                    </div>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Bekliyor</p>
                    <p class="text-lg font-semibold text-gray-900"><?php echo number_format($stats['pending']); ?></p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check text-white"></i>
                    </div>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Onaylı</p>
                    <p class="text-lg font-semibold text-gray-900"><?php echo number_format($stats['approved']); ?></p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-ban text-white"></i>
                    </div>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Spam</p>
                    <p class="text-lg font-semibold text-gray-900"><?php echo number_format($stats['spam']); ?></p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="bg-white rounded-lg shadow p-6">
        <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <!-- Search -->
            <div>
                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Ara</label>
                <input type="text" 
                       id="search" 
                       name="search" 
                       value="<?php echo clean($search); ?>"
                       placeholder="İsim, e-posta veya içerik..."
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
            </div>
            
            <!-- Status Filter -->
            <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Durum</label>
                <select id="status" name="status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                    <option value="">Tüm Durumlar</option>
                    <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Bekliyor</option>
                    <option value="approved" <?php echo $status_filter === 'approved' ? 'selected' : ''; ?>>Onaylı</option>
                    <option value="spam" <?php echo $status_filter === 'spam' ? 'selected' : ''; ?>>Spam</option>
                </select>
            </div>
            
            <!-- Post Filter -->
            <div>
                <label for="post" class="block text-sm font-medium text-gray-700 mb-1">Yazı</label>
                <select id="post" name="post" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                    <option value="">Tüm Yazılar</option>
                    <?php foreach ($posts as $post): ?>
                        <option value="<?php echo $post['id']; ?>" <?php echo $post_filter === $post['id'] ? 'selected' : ''; ?>>
                            <?php echo clean($post['title']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <!-- Actions -->
            <div class="flex items-end">
                <button type="submit" class="w-full bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                    <i class="fas fa-search mr-2"></i>Filtrele
                </button>
            </div>
        </form>
    </div>
    
    <!-- Comments Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <form id="bulk-form" method="POST" onsubmit="return handleBulkAction('bulk-form')">
            <!-- Bulk Actions -->
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <select name="bulk_action" class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                            <option value="">Toplu İşlem Seç</option>
                            <option value="approve">Onayla</option>
                            <option value="pending">Beklemede Yap</option>
                            <option value="spam">Spam İşaretle</option>
                            <option value="delete">Sil</option>
                        </select>
                        <button type="submit" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                            Uygula
                        </button>
                    </div>
                    <div class="text-sm text-gray-600">
                        <?php echo count($comments); ?> yorum gösteriliyor
                    </div>
                </div>
            </div>
            
            <!-- Comments List -->
            <div class="divide-y divide-gray-200">
                <?php if (empty($comments)): ?>
                    <div class="px-6 py-12 text-center">
                        <i class="fas fa-comments text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500 mb-4">
                            <?php if (!empty($search) || !empty($status_filter) || $post_filter > 0): ?>
                                Arama kriterlerinize uygun yorum bulunamadı.
                            <?php else: ?>
                                Henüz yorum yok.
                            <?php endif; ?>
                        </p>
                    </div>
                <?php else: ?>
                    <?php foreach ($comments as $comment): ?>
                        <div class="px-6 py-4 hover:bg-gray-50">
                            <div class="flex items-start space-x-4">
                                <!-- Checkbox -->
                                <div class="flex-shrink-0 pt-1">
                                    <input type="checkbox" name="selected[]" value="<?php echo $comment['id']; ?>" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                </div>
                                
                                <!-- Avatar -->
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                        <span class="text-sm font-semibold text-gray-600">
                                            <?php echo strtoupper(substr($comment['author_name'], 0, 1)); ?>
                                        </span>
                                    </div>
                                </div>
                                
                                <!-- Content -->
                                <div class="flex-1 min-w-0">
                                    <!-- Header -->
                                    <div class="flex items-center justify-between mb-2">
                                        <div class="flex items-center space-x-2">
                                            <h4 class="text-sm font-medium text-gray-900">
                                                <?php echo clean($comment['author_name']); ?>
                                            </h4>
                                            <span class="text-sm text-gray-500">
                                                <?php echo clean($comment['author_email']); ?>
                                            </span>
                                            <?php if (!empty($comment['author_website'])): ?>
                                                <a href="<?php echo clean($comment['author_website']); ?>" target="_blank" class="text-sm text-primary-600 hover:text-primary-700">
                                                    <i class="fas fa-external-link-alt"></i>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <!-- Status Badge -->
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php 
                                            echo $comment['status'] === 'approved' ? 'bg-green-100 text-green-800' : 
                                                ($comment['status'] === 'spam' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'); 
                                        ?>">
                                            <?php 
                                            echo $comment['status'] === 'approved' ? 'Onaylı' : 
                                                ($comment['status'] === 'spam' ? 'Spam' : 'Bekliyor'); 
                                            ?>
                                        </span>
                                    </div>
                                    
                                    <!-- Comment Content -->
                                    <div class="text-sm text-gray-700 mb-2">
                                        <?php echo nl2br(clean($comment['content'])); ?>
                                    </div>
                                    
                                    <!-- Meta Info -->
                                    <div class="flex items-center justify-between text-xs text-gray-500">
                                        <div class="flex items-center space-x-4">
                                            <span><?php echo timeAgo($comment['created_at']); ?></span>
                                            <span>IP: <?php echo clean($comment['ip_address']); ?></span>
                                            <a href="../post.php?slug=<?php echo clean($comment['post_slug']); ?>#comment-<?php echo $comment['id']; ?>" target="_blank" class="text-primary-600 hover:text-primary-700">
                                                <?php echo clean($comment['post_title']); ?>
                                            </a>
                                        </div>
                                        
                                        <!-- Quick Actions -->
                                        <div class="flex items-center space-x-2">
                                            <?php if ($comment['status'] !== 'approved'): ?>
                                                <a href="?action=approve&id=<?php echo $comment['id']; ?>" class="text-green-600 hover:text-green-700" title="Onayla">
                                                    <i class="fas fa-check"></i>
                                                </a>
                                            <?php endif; ?>
                                            
                                            <?php if ($comment['status'] !== 'spam'): ?>
                                                <a href="?action=spam&id=<?php echo $comment['id']; ?>" class="text-red-600 hover:text-red-700" title="Spam">
                                                    <i class="fas fa-ban"></i>
                                                </a>
                                            <?php endif; ?>
                                            
                                            <?php if ($comment['status'] !== 'pending'): ?>
                                                <a href="?action=pending&id=<?php echo $comment['id']; ?>" class="text-yellow-600 hover:text-yellow-700" title="Beklemede">
                                                    <i class="fas fa-clock"></i>
                                                </a>
                                            <?php endif; ?>
                                            
                                            <a href="?action=delete&id=<?php echo $comment['id']; ?>" onclick="return confirm('Bu yorumu silmek istediğinizden emin misiniz?')" class="text-red-600 hover:text-red-700" title="Sil">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </form>
        
        <!-- Pagination -->
        <?php if ($pagination['total_pages'] > 1): ?>
            <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        Toplam <?php echo number_format($total_comments); ?> yorumdan 
                        <?php echo (($page - 1) * $limit) + 1; ?>-<?php echo min($page * $limit, $total_comments); ?> arası gösteriliyor
                    </div>
                    
                    <nav class="flex space-x-2">
                        <?php if ($pagination['has_prev']): ?>
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $pagination['prev_page']])); ?>" 
                               class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                                Önceki
                            </a>
                        <?php endif; ?>
                        
                        <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                            <?php if ($i == $pagination['current_page']): ?>
                                <span class="px-3 py-2 text-sm font-medium text-white bg-primary-600 border border-primary-600 rounded-md">
                                    <?php echo $i; ?>
                                </span>
                            <?php else: ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>" 
                                   class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                                    <?php echo $i; ?>
                                </a>
                            <?php endif; ?>
                        <?php endfor; ?>
                        
                        <?php if ($pagination['has_next']): ?>
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $pagination['next_page']])); ?>" 
                               class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                                Sonraki
                            </a>
                        <?php endif; ?>
                    </nav>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
