<?php
// Post görselleri test sayfası
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once '../config.php';
require_once '../includes/ImageUploader.php';
require_once __DIR__ . '/includes/auth.php';

$db = Database::getInstance();

// Tüm postları çek
$posts = $db->fetchAll("SELECT id, title FROM posts ORDER BY created_at DESC");

// Seçilen postun görsellerini çek
$selected_post_id = isset($_GET['post_id']) ? (int)$_GET['post_id'] : null;
$post_images = [];
if ($selected_post_id) {
    $post_images = $db->fetchAll("SELECT * FROM post_images WHERE post_id = ? ORDER BY sort_order ASC, id ASC", [$selected_post_id]);
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Post Görselleri Testi</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .result { margin-top: 20px; padding: 10px; border: 1px solid #ccc; background: #f9f9f9; }
        .error { color: red; }
        .success { color: green; }
        .image-list { display: flex; flex-wrap: wrap; gap: 20px; margin-top: 20px; }
        .image-item { border: 1px solid #ddd; padding: 10px; border-radius: 8px; background: #fff; }
        .image-item img { max-width: 200px; max-height: 200px; display: block; margin-bottom: 8px; }
    </style>
</head>
<body>
    <h1>Post Görselleri Testi</h1>
    <form method="GET">
        <label>Post Seçin:
            <select name="post_id" onchange="this.form.submit()">
                <option value="">-- Seçiniz --</option>
                <?php foreach ($posts as $post): ?>
                    <option value="<?php echo $post['id']; ?>" <?php echo ($selected_post_id == $post['id']) ? 'selected' : ''; ?>>
                        <?php echo $post['id'] . ' - ' . htmlspecialchars($post['title']); ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </label>
    </form>

    <?php if ($selected_post_id): ?>
        <div class="result">
            <h2>Post ID: <?php echo $selected_post_id; ?> için Yüklenen Görseller</h2>
            <?php if (empty($post_images)): ?>
                <p class="error">Bu posta ait görsel bulunamadı.</p>
            <?php else: ?>
                <div class="image-list">
                    <?php foreach ($post_images as $img): ?>
                        <div class="image-item">
                            <img src="/<?php echo htmlspecialchars($img['file_path']); ?>" alt="<?php echo htmlspecialchars($img['alt_text']); ?>">
                            <div><strong>Dosya:</strong> <?php echo htmlspecialchars($img['filename']); ?></div>
                            <div><strong>Alt Text:</strong> <?php echo htmlspecialchars($img['alt_text']); ?></div>
                            <div><strong>Caption:</strong> <?php echo htmlspecialchars($img['caption']); ?></div>
                            <div><strong>Boyut:</strong> <?php echo $img['width'] . 'x' . $img['height']; ?></div>
                            <div><strong>Eklenme:</strong> <?php echo $img['created_at']; ?></div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>
</body>
</html>
