<?php
require_once '../config.php';

// Eğer zaten giriş yapılmışsa dashboard'a yönlendir
if (isLoggedIn()) {
    header('Location: dashboard.php');
    exit;
}

$error_message = '';

// Login form gönderildi mi?
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = clean($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // CSRF token kontrolü
    if (!verifyCSRFToken($csrf_token)) {
        $error_message = 'Güvenlik hatası. Lütfen tekrar deneyin.';
    } elseif (empty($username) || empty($password)) {
        $error_message = 'Kullanıcı adı ve şifre alanları zorunludur.';
    } else {
        try {
            $db = Database::getInstance();
            
            // Kullanıcıyı veritabanından al
            $user = $db->fetch("SELECT * FROM users WHERE username = ?", [$username]);
            
            if ($user && password_verify($password, $user['password'])) {
                // Giriş başarılı
                $_SESSION['admin_logged_in'] = true;
                $_SESSION['admin_user_id'] = $user['id'];
                $_SESSION['admin_username'] = $user['username'];
                $_SESSION['admin_full_name'] = $user['full_name'];
                
                // Son giriş zamanını güncelle
                $db->update('users', ['updated_at' => date('Y-m-d H:i:s')], 'id = ?', [$user['id']]);
                
                // Dashboard'a yönlendir
                header('Location: dashboard.php');
                exit;
            } else {
                $error_message = 'Kullanıcı adı veya şifre hatalı.';
                
                // Güvenlik için kısa bir bekleme
                sleep(1);
            }
        } catch (Exception $e) {
            error_log("Login error: " . $e->getMessage());
            $error_message = 'Giriş sırasında bir hata oluştu. Lütfen tekrar deneyin.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Giriş - ToolHane Blog</title>
    
    <!-- CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body {
            font-family: 'Poppins', sans-serif;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-primary-50 to-primary-100 min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full mx-4">
        <!-- Logo & Title -->
        <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-primary-600 rounded-full mb-4">
                <i class="fas fa-user-shield text-white text-2xl"></i>
            </div>
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Admin Paneli</h1>
            <p class="text-gray-600">ToolHane Blog Yönetim Sistemi</p>
        </div>
        
        <!-- Login Form -->
        <div class="bg-white rounded-lg shadow-lg p-8">
            <form method="POST" class="space-y-6">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                
                <!-- Error Message -->
                <?php if (!empty($error_message)): ?>
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                            <span class="text-red-700 text-sm"><?php echo clean($error_message); ?></span>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- Username Field -->
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-user mr-2"></i>Kullanıcı Adı
                    </label>
                    <input type="text" 
                           id="username" 
                           name="username" 
                           required
                           value="<?php echo isset($_POST['username']) ? clean($_POST['username']) : ''; ?>"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
                           placeholder="Kullanıcı adınızı girin">
                </div>
                
                <!-- Password Field -->
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-lock mr-2"></i>Şifre
                    </label>
                    <div class="relative">
                        <input type="password" 
                               id="password" 
                               name="password" 
                               required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors pr-12"
                               placeholder="Şifrenizi girin">
                        <button type="button" 
                                onclick="togglePassword()" 
                                class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600">
                            <i id="password-icon" class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Remember Me -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input type="checkbox" 
                               id="remember" 
                               name="remember" 
                               class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                        <label for="remember" class="ml-2 block text-sm text-gray-700">
                            Beni hatırla
                        </label>
                    </div>
                    
                    <a href="#" class="text-sm text-primary-600 hover:text-primary-700">
                        Şifremi unuttum
                    </a>
                </div>
                
                <!-- Login Button -->
                <button type="submit" 
                        class="w-full bg-primary-600 text-white py-3 px-4 rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors font-medium">
                    <i class="fas fa-sign-in-alt mr-2"></i>Giriş Yap
                </button>
            </form>
            
            <!-- Default Credentials Info -->
            <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h3 class="text-sm font-medium text-blue-800 mb-2">
                    <i class="fas fa-info-circle mr-1"></i>HOŞGELDİNİZ EMRE CAN HAZRETLERİ
                </h3>
                <div class="text-sm text-blue-700">
                    <p class="mt-2 text-xs text-blue-600">
                        <i class="fas fa-exclamation-triangle mr-1"></i>
                        İYİ BLOGLAR
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Footer Links -->
        <div class="text-center mt-8 space-y-2">
            <a href="../index.php" class="text-primary-600 hover:text-primary-700 text-sm">
                <i class="fas fa-arrow-left mr-1"></i>Blog Ana Sayfası
            </a>
            <br>
            <a href="https://www.toolhane.com" target="_blank" class="text-gray-500 hover:text-gray-700 text-sm">
                ToolHane Ana Site
            </a>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script>
        function togglePassword() {
            const passwordField = document.getElementById('password');
            const passwordIcon = document.getElementById('password-icon');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                passwordIcon.className = 'fas fa-eye-slash';
            } else {
                passwordField.type = 'password';
                passwordIcon.className = 'fas fa-eye';
            }
        }
        
        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                e.preventDefault();
                alert('Lütfen tüm alanları doldurun.');
                return false;
            }
            
            if (username.length < 3) {
                e.preventDefault();
                alert('Kullanıcı adı en az 3 karakter olmalıdır.');
                return false;
            }
            
            if (password.length < 6) {
                e.preventDefault();
                alert('Şifre en az 6 karakter olmalıdır.');
                return false;
            }
        });
        
        // Auto-focus on username field
        document.getElementById('username').focus();
        
        // Prevent multiple form submissions
        let formSubmitted = false;
        document.querySelector('form').addEventListener('submit', function() {
            if (formSubmitted) {
                return false;
            }
            formSubmitted = true;
            
            const submitButton = document.querySelector('button[type="submit"]');
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Giriş yapılıyor...';
            
            return true;
        });
    </script>
</body>
</html>
