<?php
require_once '../config.php';
require_once '../includes/ImageUploader.php';

// Sayfa bilgileri
$page_title = 'Yeni Yazı Ekle';
$page_description = 'Yeni blog yazısı oluştur';

// Veritabanı bağlantısı
$db = Database::getInstance();

// Form gönderildi mi?
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = clean($_POST['title'] ?? '');
    $slug = clean($_POST['slug'] ?? '');
    $excerpt = clean($_POST['excerpt'] ?? '');
    $content = $_POST['content'] ?? ''; // HTML içerik olduğu için clean() kullanmıyoruz
    $status = clean($_POST['status'] ?? 'draft');
    $meta_title = clean($_POST['meta_title'] ?? '');
    $meta_description = clean($_POST['meta_description'] ?? '');
    $categories = $_POST['categories'] ?? [];
    $featured_image = clean($_POST['featured_image'] ?? '');
    
    $errors = [];
    
    // Validasyon
    if (empty($title)) {
        $errors[] = 'Başlık alanı zorunludur.';
    }
    
    if (empty($content)) {
        $errors[] = 'İçerik alanı zorunludur.';
    }
    
    // Slug oluştur veya kontrol et
    if (empty($slug)) {
        $slug = createSlug($title);
    } else {
        $slug = createSlug($slug);
    }
    
    // Slug benzersizlik kontrolü
    if (!empty($slug)) {
        $existing_slug = $db->fetch("SELECT id FROM posts WHERE slug = ?", [$slug]);
        if ($existing_slug) {
            $slug = $slug . '-' . time();
        }
    }
    
    if (empty($slug)) {
        $errors[] = 'Geçerli bir slug oluşturulamadı.';
    }
    
    // CSRF token kontrolü
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Güvenlik hatası. Lütfen tekrar deneyin.';
    }
    
    if (empty($errors)) {
        try {
            $db->beginTransaction();
            
            // Yazıyı ekle
            $post_data = [
                'title' => $title,
                'slug' => $slug,
                'excerpt' => $excerpt,
                'content' => $content,
                'author_id' => $_SESSION['admin_user_id'],
                'status' => $status,
                'meta_title' => $meta_title,
                'meta_description' => $meta_description,
                'featured_image' => $featured_image
            ];
            
            $post_id = $db->insert('posts', $post_data);
            
            // Kategorileri ekle
            if (!empty($categories)) {
                foreach ($categories as $category_id) {
                    $category_id = intval($category_id);
                    if ($category_id > 0) {
                        $db->insert('post_categories', [
                            'post_id' => $post_id,
                            'category_id' => $category_id
                        ]);
                    }
                }
            }
                // Eğer featured_image doluysa, post_images tablosuna da ekle
                if (!empty($featured_image)) {
                    // Görsel yükleme işlemi: Eğer featured_image inputunda dosya adı varsa, post_images tablosuna ekle
                    if (!empty($_POST['featured_image'])) {
                        $filename = clean($_POST['featured_image']);
                        $year = date('Y', time());
                        $month = date('m', time());
                        $file_path = 'uploads/posts/original/' . $year . '/' . $month . '/' . $filename;
                        $db->insert('post_images', [
                            'post_id' => $post_id,
                            'filename' => $filename,
                            'original_filename' => $filename,
                            'file_path' => $file_path,
                            'file_size' => 0,
                            'mime_type' => '',
                            'width' => 0,
                            'height' => 0,
                            'alt_text' => '',
                            'caption' => '',
                            'is_featured' => 1,
                            'sort_order' => 0,
                            'created_at' => date('Y-m-d H:i:s'),
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);
                    }
                    // Dosya yolunu tahmin et
                    $year = date('Y', time());
                    $month = date('m', time());
                    $file_path = 'uploads/posts/original/' . $year . '/' . $month . '/' . $featured_image;
                    $db->insert('post_images', [
                        'post_id' => $post_id,
                        'filename' => $featured_image,
                        'original_filename' => $featured_image,
                        'file_path' => $file_path,
                        'file_size' => 0,
                        'mime_type' => '',
                        'width' => 0,
                        'height' => 0,
                        'alt_text' => '',
                        'caption' => '',
                        'sort_order' => 0,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                }
                // Eğer featured_image boşsa, post_images tablosundaki ilk görseli featured_image olarak güncelle
                if (empty($featured_image)) {
                    $first_image = $db->fetch("SELECT filename FROM post_images WHERE post_id = ? ORDER BY sort_order ASC, id ASC LIMIT 1", [$post_id]);
                    if ($first_image && !empty($first_image['filename'])) {
                        $db->update('posts', ['featured_image' => $first_image['filename']], 'id = ?', [$post_id]);
                    }
                }
            
            $db->commit();
            
            setFlashMessage('success', 'Yazı başarıyla oluşturuldu.');
            
            // Yazı düzenleme sayfasına yönlendir
            header('Location: post-edit.php?id=' . $post_id);
            exit;
            
        } catch (Exception $e) {
            $db->rollback();
            error_log("Post add error: " . $e->getMessage());
            setFlashMessage('error', 'Yazı oluşturulurken hata oluştu: ' . $e->getMessage());
        }
    } else {
        foreach ($errors as $error) {
            setFlashMessage('error', $error);
        }
    }
}

// Kategorileri al
try {
    $categories = $db->fetchAll("SELECT * FROM categories ORDER BY name");
} catch (Exception $e) {
    $categories = [];
}

// Header'ı dahil et
include 'includes/header.php';
?>

<!-- Post Add Form -->
<div class="max-w-4xl mx-auto">
    <form method="POST" id="post-form" class="space-y-6">
        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
        
        <!-- Main Content -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Left Column - Main Content -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Title -->
                    <div>
                        <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                            Başlık *
                        </label>
                        <input type="text" 
                               id="title" 
                               name="title" 
                               required
                               value="<?php echo isset($_POST['title']) ? clean($_POST['title']) : ''; ?>"
                               onkeyup="generateSlug(this.value, 'slug')"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-lg">
                    </div>
                    
                    <!-- Slug -->
                    <div>
                        <label for="slug" class="block text-sm font-medium text-gray-700 mb-2">
                            URL Slug
                        </label>
                        <div class="flex">
                            <span class="inline-flex items-center px-3 rounded-l-lg border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                                <?php echo SITE_URL; ?>/post.php?slug=
                            </span>
                            <input type="text" 
                                   id="slug" 
                                   name="slug"
                                   value="<?php echo isset($_POST['slug']) ? clean($_POST['slug']) : ''; ?>"
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-r-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Boş bırakılırsa başlıktan otomatik oluşturulur.</p>
                    </div>
                    
                    <!-- Excerpt -->
                    <div>
                        <label for="excerpt" class="block text-sm font-medium text-gray-700 mb-2">
                            Özet
                        </label>
                        <textarea id="excerpt" 
                                  name="excerpt" 
                                  rows="3"
                                  maxlength="300"
                                  onkeyup="updateCharCount(this, 'excerpt-count', 300)"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                                  placeholder="Yazının kısa özeti..."><?php echo isset($_POST['excerpt']) ? clean($_POST['excerpt']) : ''; ?></textarea>
                        <div class="flex justify-between text-xs text-gray-500 mt-1">
                            <span>Ana sayfada ve arama sonuçlarında gösterilir</span>
                            <span id="excerpt-count">0/300</span>
                        </div>
                    </div>
                    
                    <!-- Content -->
                    <div>
                        <label for="content" class="block text-sm font-medium text-gray-700 mb-2">
                            İçerik *
                        </label>
                        <textarea id="content" 
                                  name="content" 
                                  class="tinymce-editor"
                                  required><?php echo isset($_POST['content']) ? htmlspecialchars($_POST['content']) : ''; ?></textarea>
                    </div>
                </div>
                
                <!-- Right Column - Sidebar -->
                <div class="space-y-6">
                    <!-- Publish Box -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Yayınlama</h3>
                        
                        <!-- Featured Image -->
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Öne Çıkan Fotoğraf</label>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-primary-400 transition-colors" id="featured-image-dropzone">
                                <div id="featured-image-preview" class="hidden">
                                    <img id="featured-image-img" src="" alt="Preview" class="max-w-full h-32 object-cover rounded-lg mx-auto mb-2">
                                    <p id="featured-image-name" class="text-sm text-gray-600 mb-2"></p>
                                    <button type="button" onclick="removeFeaturedImage()" class="text-red-600 hover:text-red-800 text-sm">
                                        <i class="fas fa-trash mr-1"></i>Kaldır
                                    </button>
                                </div>
                                <div id="featured-image-upload" class="">
                                    <i class="fas fa-cloud-upload-alt text-3xl text-gray-400 mb-2"></i>
                                    <p class="text-gray-600 mb-2">Fotoğraf yüklemek için tıklayın veya sürükleyin</p>
                                    <input type="file" id="featured-image-input" accept="image/*" class="hidden">
                                    <input type="hidden" id="featured_image" name="featured_image" value="<?php echo isset($_POST['featured_image']) ? clean($_POST['featured_image']) : ''; ?>">
                                    <button type="button" onclick="document.getElementById('featured-image-input').click()" class="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors">
                                        Fotoğraf Seç
                                    </button>
                                </div>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">Maksimum 5MB, JPG, PNG, WebP formatları desteklenir</p>
                        </div>

                        <!-- Status -->
                        <div class="mb-4">
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Durum</label>
                            <select id="status" name="status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                                <option value="draft" <?php echo (isset($_POST['status']) && $_POST['status'] === 'draft') ? 'selected' : ''; ?>>Taslak</option>
                                <option value="published" <?php echo (isset($_POST['status']) && $_POST['status'] === 'published') ? 'selected' : ''; ?>>Yayınla</option>
                            </select>
                        </div>
                        
                        <!-- Actions -->
                        <div class="flex flex-col space-y-2">
                            <button type="submit" class="w-full bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 transition-colors">
                                <i class="fas fa-save mr-2"></i>Kaydet
                            </button>
                            <a href="posts.php" class="w-full bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors text-center">
                                <i class="fas fa-times mr-2"></i>İptal
                            </a>
                        </div>
                    </div>
                    
                    <!-- Categories -->
                    <?php if (!empty($categories)): ?>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Kategoriler</h3>
                            <div class="space-y-2 max-h-48 overflow-y-auto">
                                <?php foreach ($categories as $category): ?>
                                    <label class="flex items-center">
                                        <input type="checkbox" 
                                               name="categories[]" 
                                               value="<?php echo $category['id']; ?>"
                                               <?php echo (isset($_POST['categories']) && in_array($category['id'], $_POST['categories'])) ? 'checked' : ''; ?>
                                               class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                        <span class="ml-2 text-sm text-gray-700"><?php echo clean($category['name']); ?></span>
                                    </label>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <!-- SEO Settings -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">SEO Ayarları</h3>
                        
                        <!-- Meta Title -->
                        <div class="mb-4">
                            <label for="meta_title" class="block text-sm font-medium text-gray-700 mb-2">Meta Başlık</label>
                            <input type="text" 
                                   id="meta_title" 
                                   name="meta_title"
                                   maxlength="60"
                                   onkeyup="updateCharCount(this, 'meta-title-count', 60)"
                                   value="<?php echo isset($_POST['meta_title']) ? clean($_POST['meta_title']) : ''; ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                            <div class="flex justify-between text-xs text-gray-500 mt-1">
                                <span>Arama motorlarında görünecek başlık</span>
                                <span id="meta-title-count">0/60</span>
                            </div>
                        </div>
                        
                        <!-- Meta Description -->
                        <div>
                            <label for="meta_description" class="block text-sm font-medium text-gray-700 mb-2">Meta Açıklama</label>
                            <textarea id="meta_description" 
                                      name="meta_description" 
                                      rows="3"
                                      maxlength="160"
                                      onkeyup="updateCharCount(this, 'meta-desc-count', 160)"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                                      placeholder="Arama motorlarında görünecek açıklama..."><?php echo isset($_POST['meta_description']) ? clean($_POST['meta_description']) : ''; ?></textarea>
                            <div class="flex justify-between text-xs text-gray-500 mt-1">
                                <span>Arama sonuçlarında görünecek açıklama</span>
                                <span id="meta-desc-count">0/160</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<?php 
$additional_scripts = '
<script>
// Initialize character counters on page load
document.addEventListener("DOMContentLoaded", function() {
    updateCharCount(document.getElementById("excerpt"), "excerpt-count", 300);
    updateCharCount(document.getElementById("meta_title"), "meta-title-count", 60);
    updateCharCount(document.getElementById("meta_description"), "meta-desc-count", 160);

    // Auto-save functionality
    enableAutoSave("post-form", 60000); // Auto-save every minute

    // Featured image upload
    initFeaturedImageUpload();
});

// Form validation
document.getElementById("post-form").addEventListener("submit", function(e) {
    const title = document.getElementById("title").value.trim();
    const content = tinymce.get("content").getContent();
    
    if (!title) {
        e.preventDefault();
        alert("Başlık alanı zorunludur.");
        document.getElementById("title").focus();
        return false;
    }
    
    if (!content || content.trim() === "") {
        e.preventDefault();
        alert("İçerik alanı zorunludur.");
        tinymce.get("content").focus();
        return false;
    }
    
    // Disable submit button to prevent double submission
    const submitBtn = this.querySelector("button[type=submit]");
    submitBtn.disabled = true;
    submitBtn.innerHTML = "<i class=\"fas fa-spinner fa-spin mr-2\"></i>Kaydediliyor...";
    
    return true;
});

// Auto-generate meta title from title if empty
document.getElementById("title").addEventListener("input", function() {
    const metaTitleField = document.getElementById("meta_title");
    if (!metaTitleField.value) {
        metaTitleField.value = this.value.substring(0, 60);
        updateCharCount(metaTitleField, "meta-title-count", 60);
    }
});

// Auto-generate meta description from excerpt if empty
document.getElementById("excerpt").addEventListener("input", function() {
    const metaDescField = document.getElementById("meta_description");
    if (!metaDescField.value) {
        metaDescField.value = this.value.substring(0, 160);
        updateCharCount(metaDescField, "meta-desc-count", 160);
    }
});

// Featured Image Upload Functions
function initFeaturedImageUpload() {
    const input = document.getElementById("featured-image-input");
    const dropzone = document.getElementById("featured-image-dropzone");

    // File input change
    input.addEventListener("change", function(e) {
        if (e.target.files.length > 0) {
            uploadFeaturedImage(e.target.files[0]);
        }
    });

    // Drag and drop
    dropzone.addEventListener("dragover", function(e) {
        e.preventDefault();
        dropzone.classList.add("border-primary-400", "bg-primary-50");
    });

    dropzone.addEventListener("dragleave", function(e) {
        e.preventDefault();
        dropzone.classList.remove("border-primary-400", "bg-primary-50");
    });

    dropzone.addEventListener("drop", function(e) {
        e.preventDefault();
        dropzone.classList.remove("border-primary-400", "bg-primary-50");

        if (e.dataTransfer.files.length > 0) {
            uploadFeaturedImage(e.dataTransfer.files[0]);
        }
    });
}

function uploadFeaturedImage(file) {
    // File validation
    if (!file.type.startsWith("image/")) {
        alert("Lütfen sadece resim dosyası seçin.");
        return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB
        alert("Dosya boyutu 5MB\'dan büyük olamaz.");
        return;
    }

    const formData = new FormData();
    formData.append("image", file);
    formData.append("alt_text", "");
    formData.append("caption", "");
    formData.append("post_id", ""); // yeni post eklenmeden önce boş

    // Show loading
    const uploadDiv = document.getElementById("featured-image-upload");
    const originalContent = uploadDiv.innerHTML;
    uploadDiv.innerHTML = `
        <div class="flex items-center justify-center">
            <i class="fas fa-spinner fa-spin text-2xl text-primary-600 mr-2"></i>
            <span class="text-primary-600">Yükleniyor...</span>
        </div>
    `;

    fetch("upload-image.php", {
        method: "POST",
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showFeaturedImagePreview(data.thumbnail, file.name, data.filename);
                if (document.getElementById("featured_image")) {
                    document.getElementById("featured_image").value = data.filename;
                }
        } else {
            alert("Yükleme hatası: " + data.error);
            uploadDiv.innerHTML = originalContent;
        }
    })
    .catch(error => {
        console.error("Upload error:", error);
        alert("Yükleme sırasında hata oluştu.");
        uploadDiv.innerHTML = originalContent;
    });
}

function showFeaturedImagePreview(imageSrc, fileName, imageValue) {
    document.getElementById("featured-image-img").src = imageSrc;
    document.getElementById("featured-image-name").textContent = fileName;
    document.getElementById("featured-image-preview").classList.remove("hidden");
    document.getElementById("featured-image-upload").classList.add("hidden");
}

function removeFeaturedImage() {
    document.getElementById("featured-image-preview").classList.add("hidden");
    document.getElementById("featured-image-upload").classList.remove("hidden");
    document.getElementById("featured_image").value = "";
    document.getElementById("featured-image-input").value = "";
}
</script>
';

include 'includes/footer.php'; 
?>
