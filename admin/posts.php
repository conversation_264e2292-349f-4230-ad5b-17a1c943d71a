<?php
require_once '../config.php';

// Sayfa bilgileri
$page_title = 'Blog Yazıları';
$page_description = 'Tüm blog yazılarını görüntüle ve yönet';

// Veritabanı bağlantısı
$db = Database::getInstance();

// Bulk actions işlemi
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['bulk_action'])) {
    $action = clean($_POST['bulk_action']);
    $selected_ids = $_POST['selected'] ?? [];
    
    if (!empty($selected_ids) && !empty($action)) {
        try {
            foreach ($selected_ids as $id) {
                $id = intval($id);
                
                switch ($action) {
                    case 'delete':
                        $db->delete('posts', 'id = ?', [$id]);
                        break;
                    case 'publish':
                        $db->update('posts', ['status' => 'published'], 'id = ?', [$id]);
                        break;
                    case 'draft':
                        $db->update('posts', ['status' => 'draft'], 'id = ?', [$id]);
                        break;
                }
            }
            
            $action_text = [
                'delete' => 'silindi',
                'publish' => 'yayınlandı',
                'draft' => 'taslak yapıldı'
            ];
            
            setFlashMessage('success', count($selected_ids) . ' yazı ' . $action_text[$action] . '.');
        } catch (Exception $e) {
            setFlashMessage('error', 'İşlem sırasında hata oluştu: ' . $e->getMessage());
        }
    }
    
    header('Location: posts.php');
    exit;
}

// Filtreleme ve sıralama
$status_filter = isset($_GET['status']) ? clean($_GET['status']) : '';
$search = isset($_GET['search']) ? clean($_GET['search']) : '';
$sort = isset($_GET['sort']) ? clean($_GET['sort']) : 'created_at';
$order = isset($_GET['order']) ? clean($_GET['order']) : 'DESC';

// Sayfalama
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

try {
    // WHERE koşulları oluştur
    $where_conditions = [];
    $params = [];
    
    if (!empty($status_filter)) {
        $where_conditions[] = 'p.status = ?';
        $params[] = $status_filter;
    }
    
    if (!empty($search)) {
        $where_conditions[] = '(p.title LIKE ? OR p.content LIKE ?)';
        $params[] = '%' . $search . '%';
        $params[] = '%' . $search . '%';
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // Toplam yazı sayısını al
    $count_sql = "SELECT COUNT(*) FROM posts p $where_clause";
    $total_posts = $db->fetch($count_sql, $params)['COUNT(*)'];
    
    // Yazıları al
    $valid_sorts = ['title', 'created_at', 'updated_at', 'views', 'status'];
    $sort = in_array($sort, $valid_sorts) ? $sort : 'created_at';
    $order = strtoupper($order) === 'ASC' ? 'ASC' : 'DESC';
    
    $posts_sql = "
        SELECT p.*, u.full_name as author_name, u.username as author_username,
               (SELECT COUNT(*) FROM comments c WHERE c.post_id = p.id) as comment_count
        FROM posts p 
        LEFT JOIN users u ON p.author_id = u.id 
        $where_clause
        ORDER BY p.$sort $order
        LIMIT ? OFFSET ?
    ";
    
    $posts_params = array_merge($params, [$limit, $offset]);
    $posts = $db->fetchAll($posts_sql, $posts_params);
    
    // Sayfalama hesapla
    $pagination = calculatePagination($page, $total_posts, $limit);
    
} catch (Exception $e) {
    error_log("Posts page error: " . $e->getMessage());
    $posts = [];
    $pagination = ['total_pages' => 1, 'current_page' => 1];
    setFlashMessage('error', 'Yazılar yüklenirken hata oluştu.');
}

// Header'ı dahil et
include 'includes/header.php';
?>

<!-- Posts Management -->
<div class="space-y-6">
    <!-- Header Actions -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Blog Yazıları</h1>
            <p class="text-gray-600">Toplam <?php echo number_format($total_posts); ?> yazı</p>
        </div>
        <div class="mt-4 sm:mt-0">
            <a href="post-add.php" class="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors">
                <i class="fas fa-plus mr-2"></i>Yeni Yazı Ekle
            </a>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="bg-white rounded-lg shadow p-6">
        <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <!-- Search -->
            <div>
                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Ara</label>
                <input type="text" 
                       id="search" 
                       name="search" 
                       value="<?php echo clean($search); ?>"
                       placeholder="Başlık veya içerik..."
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
            </div>
            
            <!-- Status Filter -->
            <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Durum</label>
                <select id="status" name="status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                    <option value="">Tüm Durumlar</option>
                    <option value="published" <?php echo $status_filter === 'published' ? 'selected' : ''; ?>>Yayında</option>
                    <option value="draft" <?php echo $status_filter === 'draft' ? 'selected' : ''; ?>>Taslak</option>
                </select>
            </div>
            
            <!-- Sort -->
            <div>
                <label for="sort" class="block text-sm font-medium text-gray-700 mb-1">Sırala</label>
                <select id="sort" name="sort" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                    <option value="created_at" <?php echo $sort === 'created_at' ? 'selected' : ''; ?>>Oluşturma Tarihi</option>
                    <option value="updated_at" <?php echo $sort === 'updated_at' ? 'selected' : ''; ?>>Güncelleme Tarihi</option>
                    <option value="title" <?php echo $sort === 'title' ? 'selected' : ''; ?>>Başlık</option>
                    <option value="views" <?php echo $sort === 'views' ? 'selected' : ''; ?>>Görüntülenme</option>
                </select>
            </div>
            
            <!-- Actions -->
            <div class="flex items-end">
                <button type="submit" class="w-full bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                    <i class="fas fa-search mr-2"></i>Filtrele
                </button>
            </div>
        </form>
    </div>
    
    <!-- Posts Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <form id="bulk-form" method="POST" onsubmit="return handleBulkAction('bulk-form')">
            <!-- Bulk Actions -->
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <select name="bulk_action" class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                            <option value="">Toplu İşlem Seç</option>
                            <option value="publish">Yayınla</option>
                            <option value="draft">Taslak Yap</option>
                            <option value="delete">Sil</option>
                        </select>
                        <button type="submit" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                            Uygula
                        </button>
                    </div>
                    <div class="text-sm text-gray-600">
                        <?php echo count($posts); ?> yazı gösteriliyor
                    </div>
                </div>
            </div>
            
            <!-- Table -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left">
                                <input type="checkbox" onchange="toggleSelectAll(this)" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['sort' => 'title', 'order' => $sort === 'title' && $order === 'ASC' ? 'DESC' : 'ASC'])); ?>" class="hover:text-gray-700">
                                    Başlık
                                    <?php if ($sort === 'title'): ?>
                                        <i class="fas fa-sort-<?php echo $order === 'ASC' ? 'up' : 'down'; ?> ml-1"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Yazar</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Durum</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['sort' => 'views', 'order' => $sort === 'views' && $order === 'ASC' ? 'DESC' : 'ASC'])); ?>" class="hover:text-gray-700">
                                    Görüntülenme
                                    <?php if ($sort === 'views'): ?>
                                        <i class="fas fa-sort-<?php echo $order === 'ASC' ? 'up' : 'down'; ?> ml-1"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['sort' => 'created_at', 'order' => $sort === 'created_at' && $order === 'ASC' ? 'DESC' : 'ASC'])); ?>" class="hover:text-gray-700">
                                    Tarih
                                    <?php if ($sort === 'created_at'): ?>
                                        <i class="fas fa-sort-<?php echo $order === 'ASC' ? 'up' : 'down'; ?> ml-1"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">İşlemler</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php if (empty($posts)): ?>
                            <tr>
                                <td colspan="7" class="px-6 py-12 text-center">
                                    <i class="fas fa-file-alt text-4xl text-gray-300 mb-4"></i>
                                    <p class="text-gray-500 mb-4">
                                        <?php if (!empty($search) || !empty($status_filter)): ?>
                                            Arama kriterlerinize uygun yazı bulunamadı.
                                        <?php else: ?>
                                            Henüz blog yazısı yok.
                                        <?php endif; ?>
                                    </p>
                                    <a href="post-add.php" class="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors">
                                        <i class="fas fa-plus mr-2"></i>İlk yazıyı oluştur
                                    </a>
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($posts as $post): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4">
                                        <input type="checkbox" name="selected[]" value="<?php echo $post['id']; ?>" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="flex items-start">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">
                                                    <a href="post-edit.php?id=<?php echo $post['id']; ?>" class="hover:text-primary-600">
                                                        <?php echo clean($post['title']); ?>
                                                    </a>
                                                </div>
                                                <div class="text-sm text-gray-500 mt-1">
                                                    <?php echo clean(excerpt($post['excerpt'] ?: $post['content'], 100)); ?>
                                                </div>
                                                <div class="flex items-center space-x-4 mt-2">
                                                    <a href="post-edit.php?id=<?php echo $post['id']; ?>" class="text-primary-600 hover:text-primary-700 text-xs">Düzenle</a>
                                                    <a href="../post.php?slug=<?php echo clean($post['slug']); ?>" target="_blank" class="text-gray-600 hover:text-gray-700 text-xs">Görüntüle</a>
                                                    <a href="post-delete.php?id=<?php echo $post['id']; ?>" onclick="return confirm('Bu yazıyı silmek istediğinizden emin misiniz?')" class="text-red-600 hover:text-red-700 text-xs">Sil</a>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-900">
                                        <?php echo clean($post['author_name'] ?: $post['author_username']); ?>
                                    </td>
                                    <td class="px-6 py-4">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $post['status'] === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'; ?>">
                                            <?php echo $post['status'] === 'published' ? 'Yayında' : 'Taslak'; ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-900">
                                        <div class="flex items-center">
                                            <i class="fas fa-eye text-gray-400 mr-1"></i>
                                            <?php echo number_format($post['views']); ?>
                                        </div>
                                        <div class="flex items-center mt-1">
                                            <i class="fas fa-comments text-gray-400 mr-1"></i>
                                            <?php echo $post['comment_count']; ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-500">
                                        <div><?php echo formatDate($post['created_at'], 'd.m.Y'); ?></div>
                                        <div class="text-xs"><?php echo formatDate($post['created_at'], 'H:i'); ?></div>
                                    </td>
                                    <td class="px-6 py-4 text-sm font-medium">
                                        <div class="flex items-center space-x-2">
                                            <a href="post-edit.php?id=<?php echo $post['id']; ?>" class="text-primary-600 hover:text-primary-700">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="../post.php?slug=<?php echo clean($post['slug']); ?>" target="_blank" class="text-gray-600 hover:text-gray-700">
                                                <i class="fas fa-external-link-alt"></i>
                                            </a>
                                            <a href="post-delete.php?id=<?php echo $post['id']; ?>" onclick="return confirm('Bu yazıyı silmek istediğinizden emin misiniz?')" class="text-red-600 hover:text-red-700">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </form>
        
        <!-- Pagination -->
        <?php if ($pagination['total_pages'] > 1): ?>
            <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        Toplam <?php echo number_format($total_posts); ?> yazıdan 
                        <?php echo (($page - 1) * $limit) + 1; ?>-<?php echo min($page * $limit, $total_posts); ?> arası gösteriliyor
                    </div>
                    
                    <nav class="flex space-x-2">
                        <?php if ($pagination['has_prev']): ?>
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $pagination['prev_page']])); ?>" 
                               class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                                Önceki
                            </a>
                        <?php endif; ?>
                        
                        <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                            <?php if ($i == $pagination['current_page']): ?>
                                <span class="px-3 py-2 text-sm font-medium text-white bg-primary-600 border border-primary-600 rounded-md">
                                    <?php echo $i; ?>
                                </span>
                            <?php else: ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>" 
                                   class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                                    <?php echo $i; ?>
                                </a>
                            <?php endif; ?>
                        <?php endfor; ?>
                        
                        <?php if ($pagination['has_next']): ?>
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $pagination['next_page']])); ?>" 
                               class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                                Sonraki
                            </a>
                        <?php endif; ?>
                    </nav>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
