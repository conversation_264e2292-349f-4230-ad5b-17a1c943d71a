<?php
require_once '../config.php';

// Sayfa bilgileri
$page_title = 'Dashboard';
$page_description = 'Blog istatistikleri ve genel bakış';

// Veritabanı bağlantısı
$db = Database::getInstance();

try {
    // İstatistikleri al
    $stats = [
        'total_posts' => $db->count('posts'),
        'published_posts' => $db->count('posts', 'status = ?', ['published']),
        'draft_posts' => $db->count('posts', 'status = ?', ['draft']),
        'total_comments' => $db->count('comments'),
        'pending_comments' => $db->count('comments', 'status = ?', ['pending']),
        'approved_comments' => $db->count('comments', 'status = ?', ['approved']),
        'total_views' => $db->fetch("SELECT SUM(views) as total FROM posts")['total'] ?? 0
    ];
    
    // Son yazıları al
    $recent_posts = $db->fetchAll("
        SELECT p.*, u.full_name as author_name 
        FROM posts p 
        LEFT JOIN users u ON p.author_id = u.id 
        ORDER BY p.created_at DESC 
        LIMIT 5
    ");
    
    // Son yorumları al
    $recent_comments = $db->fetchAll("
        SELECT c.*, p.title as post_title, p.slug as post_slug
        FROM comments c 
        LEFT JOIN posts p ON c.post_id = p.id 
        ORDER BY c.created_at DESC 
        LIMIT 5
    ");
    
    // Popüler yazıları al
    $popular_posts = $db->fetchAll("
        SELECT p.*, u.full_name as author_name 
        FROM posts p 
        LEFT JOIN users u ON p.author_id = u.id 
        WHERE p.status = 'published'
        ORDER BY p.views DESC 
        LIMIT 5
    ");
    
    // Aylık istatistikler (son 6 ay)
    $monthly_stats = [];
    for ($i = 5; $i >= 0; $i--) {
        $month = date('Y-m', strtotime("-$i months"));
        $month_name = date('M Y', strtotime("-$i months"));
        
        $posts_count = $db->count('posts', 'DATE_FORMAT(created_at, "%Y-%m") = ?', [$month]);
        $comments_count = $db->count('comments', 'DATE_FORMAT(created_at, "%Y-%m") = ?', [$month]);
        
        $monthly_stats[] = [
            'month' => $month_name,
            'posts' => $posts_count,
            'comments' => $comments_count
        ];
    }
    
} catch (Exception $e) {
    error_log("Dashboard error: " . $e->getMessage());
    $stats = array_fill_keys(['total_posts', 'published_posts', 'draft_posts', 'total_comments', 'pending_comments', 'approved_comments', 'total_views'], 0);
    $recent_posts = [];
    $recent_comments = [];
    $popular_posts = [];
    $monthly_stats = [];
}

// Header'ı dahil et
include 'includes/header.php';
?>

<!-- Dashboard Content -->
<div class="space-y-6">
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Posts -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-file-alt text-white text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-900"><?php echo number_format($stats['total_posts']); ?></h3>
                    <p class="text-gray-600">Toplam Yazı</p>
                </div>
            </div>
            <div class="mt-4 flex items-center text-sm">
                <span class="text-green-600">
                    <i class="fas fa-check-circle mr-1"></i>
                    <?php echo $stats['published_posts']; ?> yayında
                </span>
                <span class="text-gray-400 mx-2">•</span>
                <span class="text-yellow-600">
                    <?php echo $stats['draft_posts']; ?> taslak
                </span>
            </div>
        </div>
        
        <!-- Total Comments -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-comments text-white text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-900"><?php echo number_format($stats['total_comments']); ?></h3>
                    <p class="text-gray-600">Toplam Yorum</p>
                </div>
            </div>
            <div class="mt-4 flex items-center text-sm">
                <span class="text-green-600">
                    <i class="fas fa-check-circle mr-1"></i>
                    <?php echo $stats['approved_comments']; ?> onaylı
                </span>
                <span class="text-gray-400 mx-2">•</span>
                <span class="text-orange-600">
                    <?php echo $stats['pending_comments']; ?> bekliyor
                </span>
            </div>
        </div>
        
        <!-- Total Views -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-eye text-white text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-900"><?php echo number_format($stats['total_views']); ?></h3>
                    <p class="text-gray-600">Toplam Görüntülenme</p>
                </div>
            </div>
            <div class="mt-4">
                <a href="posts.php" class="text-purple-600 hover:text-purple-700 text-sm font-medium">
                    Detayları görüntüle <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center mb-4">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-primary-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-plus text-white text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-900">Hızlı İşlemler</h3>
                </div>
            </div>
            <div class="space-y-2">
                <a href="post-add.php" class="block w-full bg-primary-600 text-white text-center py-2 rounded-lg hover:bg-primary-700 transition-colors">
                    <i class="fas fa-plus mr-2"></i>Yeni Yazı
                </a>
                <a href="comments.php" class="block w-full bg-gray-600 text-white text-center py-2 rounded-lg hover:bg-gray-700 transition-colors">
                    <i class="fas fa-comments mr-2"></i>Yorumları Yönet
                </a>
            </div>
        </div>
    </div>
    
    <!-- Charts and Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Monthly Stats Chart -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Aylık İstatistikler</h3>
            <div class="space-y-4">
                <?php foreach ($monthly_stats as $stat): ?>
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600"><?php echo $stat['month']; ?></span>
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                                <span class="text-sm text-gray-600"><?php echo $stat['posts']; ?> yazı</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                                <span class="text-sm text-gray-600"><?php echo $stat['comments']; ?> yorum</span>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        
        <!-- Recent Comments -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Son Yorumlar</h3>
                <a href="comments.php" class="text-primary-600 hover:text-primary-700 text-sm font-medium">
                    Tümünü gör <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
            
            <?php if (empty($recent_comments)): ?>
                <div class="text-center py-8">
                    <i class="fas fa-comments text-4xl text-gray-300 mb-2"></i>
                    <p class="text-gray-500">Henüz yorum yok</p>
                </div>
            <?php else: ?>
                <div class="space-y-4">
                    <?php foreach ($recent_comments as $comment): ?>
                        <div class="border-b border-gray-200 pb-4 last:border-b-0">
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                                        <span class="text-xs font-semibold text-gray-600">
                                            <?php echo strtoupper(substr($comment['author_name'], 0, 1)); ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center space-x-2">
                                        <p class="text-sm font-medium text-gray-900"><?php echo clean($comment['author_name']); ?></p>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium <?php echo $comment['status'] === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'; ?>">
                                            <?php echo $comment['status'] === 'pending' ? 'Bekliyor' : 'Onaylı'; ?>
                                        </span>
                                    </div>
                                    <p class="text-sm text-gray-600 mt-1"><?php echo clean(excerpt($comment['content'], 80)); ?></p>
                                    <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                                        <span><?php echo timeAgo($comment['created_at']); ?></span>
                                        <a href="../post.php?slug=<?php echo clean($comment['post_slug']); ?>" target="_blank" class="text-primary-600 hover:text-primary-700">
                                            <?php echo clean($comment['post_title']); ?>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Recent Posts and Popular Posts -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent Posts -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Son Yazılar</h3>
                <a href="posts.php" class="text-primary-600 hover:text-primary-700 text-sm font-medium">
                    Tümünü gör <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
            
            <?php if (empty($recent_posts)): ?>
                <div class="text-center py-8">
                    <i class="fas fa-file-alt text-4xl text-gray-300 mb-2"></i>
                    <p class="text-gray-500">Henüz yazı yok</p>
                    <a href="post-add.php" class="inline-block mt-2 bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors">
                        İlk yazıyı oluştur
                    </a>
                </div>
            <?php else: ?>
                <div class="space-y-4">
                    <?php foreach ($recent_posts as $post): ?>
                        <div class="border-b border-gray-200 pb-4 last:border-b-0">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <h4 class="text-sm font-medium text-gray-900 mb-1">
                                        <a href="post-edit.php?id=<?php echo $post['id']; ?>" class="hover:text-primary-600">
                                            <?php echo clean($post['title']); ?>
                                        </a>
                                    </h4>
                                    <div class="flex items-center space-x-4 text-xs text-gray-500">
                                        <span><?php echo formatDate($post['created_at'], 'd.m.Y'); ?></span>
                                        <span><?php echo clean($post['author_name']); ?></span>
                                        <span><?php echo number_format($post['views']); ?> görüntülenme</span>
                                    </div>
                                </div>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium <?php echo $post['status'] === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'; ?>">
                                    <?php echo $post['status'] === 'published' ? 'Yayında' : 'Taslak'; ?>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Popular Posts -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Popüler Yazılar</h3>
                <a href="posts.php?sort=views" class="text-primary-600 hover:text-primary-700 text-sm font-medium">
                    Tümünü gör <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
            
            <?php if (empty($popular_posts)): ?>
                <div class="text-center py-8">
                    <i class="fas fa-chart-line text-4xl text-gray-300 mb-2"></i>
                    <p class="text-gray-500">Henüz popüler yazı yok</p>
                </div>
            <?php else: ?>
                <div class="space-y-4">
                    <?php foreach ($popular_posts as $index => $post): ?>
                        <div class="flex items-center space-x-3">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-sm font-semibold">
                                    <?php echo $index + 1; ?>
                                </div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <h4 class="text-sm font-medium text-gray-900 truncate">
                                    <a href="../post.php?slug=<?php echo clean($post['slug']); ?>" target="_blank" class="hover:text-primary-600">
                                        <?php echo clean($post['title']); ?>
                                    </a>
                                </h4>
                                <div class="flex items-center space-x-2 text-xs text-gray-500">
                                    <span><?php echo number_format($post['views']); ?> görüntülenme</span>
                                    <span>•</span>
                                    <span><?php echo formatDate($post['created_at'], 'd.m.Y'); ?></span>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
