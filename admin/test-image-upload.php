<?php
// Görsel yükleme test sayfası
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once '../config.php';
require_once '../includes/ImageUploader.php';
require_once __DIR__ . '/includes/auth.php';

?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Görsel Yükleme Testi</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .result { margin-top: 20px; padding: 10px; border: 1px solid #ccc; background: #f9f9f9; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>Görsel Yükleme Testi</h1>
    <form method="POST" enctype="multipart/form-data">
        <label>Görsel seçin: <input type="file" name="image" required></label><br><br>
        <label>Post ID (isteğe bağlı): <input type="text" name="post_id"></label><br><br>
        <label>Alt Text: <input type="text" name="alt_text"></label><br><br>
        <label>Caption: <input type="text" name="caption"></label><br><br>
        <button type="submit">Yükle</button>
    </form>

    <div class="result">
    <?php
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        try {
            $uploader = new ImageUploader();
            if (!isset($_FILES['image'])) {
                echo '<div class="error">Dosya seçilmedi</div>';
                exit;
            }
            if ($_FILES['image']['error'] !== UPLOAD_ERR_OK) {
                echo '<div class="error">Dosya yükleme hatası: ' . $_FILES['image']['error'] . '</div>';
                exit;
            }
            $postId = isset($_POST['post_id']) ? (int)$_POST['post_id'] : null;
            $altText = isset($_POST['alt_text']) ? $_POST['alt_text'] : '';
            $caption = isset($_POST['caption']) ? $_POST['caption'] : '';
            $result = $uploader->uploadImage($_FILES['image'], $postId, $altText, $caption);
            if ($result['success']) {
                echo '<div class="success">Yükleme başarılı!</div>';
                echo '<pre>' . print_r($result, true) . '</pre>';
            } else {
                echo '<div class="error">Yükleme başarısız: ' . $result['error'] . '</div>';
                echo '<pre>' . print_r($result, true) . '</pre>';
            }
        } catch (Exception $e) {
            echo '<div class="error">Exception: ' . $e->getMessage() . '</div>';
        }
    }
    ?>
    </div>
</body>
</html>
