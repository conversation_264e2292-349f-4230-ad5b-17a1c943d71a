            </main>
        </div>
    </div>
    
    <!-- Alpine.js for dropdown functionality -->
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    
    <!-- Admin Panel JavaScript -->
    <script>
        // Confirm delete actions
        function confirmDelete(message = 'Bu işlemi gerçekleştirmek istediğinizden emin misiniz?') {
            return confirm(message);
        }
        
        // Auto-hide flash messages
        document.addEventListener('DOMContentLoaded', function() {
            const flashMessages = document.querySelectorAll('.flash-message');
            flashMessages.forEach(message => {
                setTimeout(() => {
                    message.style.opacity = '0';
                    setTimeout(() => {
                        message.remove();
                    }, 300);
                }, 5000);
            });

            // Mobile sidebar toggle functionality
            const sidebarToggle = document.getElementById('mobile-sidebar-toggle');
            const sidebar = document.getElementById('admin-sidebar');
            const overlay = document.getElementById('sidebar-overlay');

            if (sidebarToggle && sidebar && overlay) {
                // Toggle sidebar
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('active');
                    overlay.classList.toggle('active');
                    document.body.style.overflow = sidebar.classList.contains('active') ? 'hidden' : '';
                });

                // Close sidebar when clicking overlay
                overlay.addEventListener('click', function() {
                    sidebar.classList.remove('active');
                    overlay.classList.remove('active');
                    document.body.style.overflow = '';
                });

                // Close sidebar when clicking a link (mobile)
                const sidebarLinks = sidebar.querySelectorAll('.sidebar-link');
                sidebarLinks.forEach(link => {
                    link.addEventListener('click', function() {
                        if (window.innerWidth <= 768) {
                            sidebar.classList.remove('active');
                            overlay.classList.remove('active');
                            document.body.style.overflow = '';
                        }
                    });
                });

                // Handle window resize
                window.addEventListener('resize', function() {
                    if (window.innerWidth > 768) {
                        sidebar.classList.remove('active');
                        overlay.classList.remove('active');
                        document.body.style.overflow = '';
                    }
                });
            }
        });
        
        // Form validation helper
        function validateForm(formId) {
            const form = document.getElementById(formId);
            if (!form) return true;
            
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('border-red-500');
                    
                    // Remove error styling on input
                    field.addEventListener('input', function() {
                        this.classList.remove('border-red-500');
                    });
                } else {
                    field.classList.remove('border-red-500');
                }
            });
            
            if (!isValid) {
                alert('Lütfen tüm zorunlu alanları doldurun.');
            }
            
            return isValid;
        }
        
        // Initialize TinyMCE for content editors
        function initTinyMCE(selector = '.tinymce-editor') {
            if (typeof tinymce !== 'undefined') {
                tinymce.init({
                    selector: selector,
                    height: 400,
                    menubar: false,
                    plugins: [
                        'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                        'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                        'insertdatetime', 'media', 'table', 'help', 'wordcount'
                    ],
                    toolbar: 'undo redo | blocks | ' +
                        'bold italic backcolor | alignleft aligncenter ' +
                        'alignright alignjustify | bullist numlist outdent indent | ' +
                        'image | removeformat | help',
                    content_style: 'body { font-family: Poppins, sans-serif; font-size: 14px }',
                    images_upload_handler: function (blobInfo, success, failure) {
                        const formData = new FormData();
                        formData.append('image', blobInfo.blob(), blobInfo.filename());
                        formData.append('alt_text', '');
                        formData.append('caption', '');

                        fetch('upload-image.php', {
                            method: 'POST',
                            body: formData
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                success(data.medium); // Medium boyutunu kullan
                            } else {
                                failure('Yükleme hatası: ' + data.error);
                            }
                        })
                        .catch(error => {
                            failure('Yükleme sırasında hata oluştu: ' + error.message);
                        });
                    },
                    automatic_uploads: true,
                    file_picker_types: 'image',
                    setup: function(editor) {
                        editor.on('change', function() {
                            editor.save();
                        });
                    }
                });
            }
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initTinyMCE();
        });
        
        // Bulk actions handler
        function handleBulkAction(formId) {
            const form = document.getElementById(formId);
            if (!form) return false;
            
            const action = form.querySelector('select[name="bulk_action"]').value;
            const checkboxes = form.querySelectorAll('input[type="checkbox"]:checked');
            
            if (!action) {
                alert('Lütfen bir işlem seçin.');
                return false;
            }
            
            if (checkboxes.length === 0) {
                alert('Lütfen en az bir öğe seçin.');
                return false;
            }
            
            const actionText = {
                'delete': 'silmek',
                'publish': 'yayınlamak',
                'draft': 'taslak yapmak',
                'approve': 'onaylamak',
                'spam': 'spam olarak işaretlemek'
            };
            
            const confirmText = actionText[action] || 'işlem yapmak';
            
            return confirm(`Seçili öğeleri ${confirmText} istediğinizden emin misiniz?`);
        }
        
        // Select all checkboxes
        function toggleSelectAll(masterCheckbox) {
            const checkboxes = document.querySelectorAll('input[type="checkbox"][name="selected[]"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = masterCheckbox.checked;
            });
        }
        
        // Auto-save draft functionality
        let autoSaveTimer;
        function enableAutoSave(formId, interval = 30000) {
            const form = document.getElementById(formId);
            if (!form) return;
            
            autoSaveTimer = setInterval(() => {
                const formData = new FormData(form);
                formData.append('auto_save', '1');
                
                fetch(form.action, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('Taslak otomatik kaydedildi', 'success');
                    }
                })
                .catch(error => {
                    console.error('Auto-save error:', error);
                });
            }, interval);
        }
        
        function disableAutoSave() {
            if (autoSaveTimer) {
                clearInterval(autoSaveTimer);
            }
        }
        
        // Notification system
        function showNotification(message, type = 'info', duration = 3000) {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;
            
            const colors = {
                success: 'bg-green-500 text-white',
                error: 'bg-red-500 text-white',
                warning: 'bg-yellow-500 text-white',
                info: 'bg-blue-500 text-white'
            };
            
            notification.className += ` ${colors[type] || colors.info}`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            // Show notification
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);
            
            // Hide notification
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, duration);
        }
        
        // Image preview functionality
        function previewImage(input, previewId) {
            const file = input.files[0];
            const preview = document.getElementById(previewId);
            
            if (file && preview) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(file);
            }
        }
        
        // Slug generator
        function generateSlug(text, targetId) {
            const target = document.getElementById(targetId);
            if (!target) return;
            
            // Turkish character mapping
            const turkishChars = {
                'ç': 'c', 'ğ': 'g', 'ı': 'i', 'ö': 'o', 'ş': 's', 'ü': 'u',
                'Ç': 'c', 'Ğ': 'g', 'I': 'i', 'İ': 'i', 'Ö': 'o', 'Ş': 's', 'Ü': 'u'
            };
            
            let slug = text.toLowerCase();
            
            // Replace Turkish characters
            Object.keys(turkishChars).forEach(char => {
                slug = slug.replace(new RegExp(char, 'g'), turkishChars[char]);
            });
            
            // Remove special characters and replace spaces with hyphens
            slug = slug
                .replace(/[^a-z0-9\s-]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .replace(/^-|-$/g, '');
            
            target.value = slug;
        }
        
        // Character counter
        function updateCharCount(input, counterId, maxLength) {
            const counter = document.getElementById(counterId);
            if (!counter) return;
            
            const currentLength = input.value.length;
            counter.textContent = `${currentLength}/${maxLength}`;
            
            if (currentLength > maxLength * 0.9) {
                counter.classList.add('text-red-500');
            } else if (currentLength > maxLength * 0.7) {
                counter.classList.add('text-yellow-500');
                counter.classList.remove('text-red-500');
            } else {
                counter.classList.remove('text-red-500', 'text-yellow-500');
            }
        }
        
        // Search functionality
        function initSearch(inputId, resultsId) {
            const input = document.getElementById(inputId);
            const results = document.getElementById(resultsId);
            
            if (!input || !results) return;
            
            let searchTimeout;
            
            input.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                const query = this.value.trim();
                
                if (query.length >= 2) {
                    searchTimeout = setTimeout(() => {
                        performAdminSearch(query, results);
                    }, 300);
                } else {
                    results.innerHTML = '';
                    results.classList.add('hidden');
                }
            });
        }
        
        function performAdminSearch(query, resultsContainer) {
            // This would be implemented based on specific search needs
            console.log('Searching for:', query);
        }
    </script>
    
    <?php if (isset($additional_scripts)): ?>
        <?php echo $additional_scripts; ?>
    <?php endif; ?>
</body>
</html>
