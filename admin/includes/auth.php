<?php
// Admin authentication check
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['admin_user_id']) || !isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    // If it's an AJAX request, return JSON error
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'error' => 'Oturum süresi dolmuş. Lütfen tekrar giriş yapın.']);
        exit;
    }

    // Regular request, redirect to login
    header('Location: login.php');
    exit;
}

// Get admin user info
try {
    $db = Database::getInstance();
    $admin_user = $db->fetch("SELECT * FROM users WHERE id = ?", [$_SESSION['admin_user_id']]);

    if (!$admin_user) {
        session_destroy();
        header('Location: login.php');
        exit;
    }
} catch (Exception $e) {
    error_log("Auth error: " . $e->getMessage());
    session_destroy();
    header('Location: login.php');
    exit;
}
?>
