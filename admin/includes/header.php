<?php
// Admin girişi kontrolü
requireLogin();

// Kullanıcı bilgilerini al
$admin_user = [
    'id' => $_SESSION['admin_user_id'],
    'username' => $_SESSION['admin_username'],
    'full_name' => $_SESSION['admin_full_name']
];
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?>Admin Panel - ToolHane Blog</title>
    
    <!-- CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- TinyMCE Editor -->
    <script src="https://cdn.tiny.cloud/1/qujeu28u66ey3nvwrxcmd22eidccam3fprfxck7egy3ugkuh/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>
    
    <style>
        body {
            font-family: 'Poppins', sans-serif;
        }

        .sidebar-link.active {
            background-color: #0ea5e9;
            color: white;
        }

        .sidebar-link:hover {
            background-color: #e0f2fe;
            color: #0369a1;
        }

        .sidebar-link.active:hover {
            background-color: #0284c7;
            color: white;
        }

        /* Mobile Sidebar Styles */
        @media (max-width: 768px) {
            .admin-sidebar {
                position: fixed;
                top: 0;
                left: -100%;
                height: 100vh;
                z-index: 50;
                transition: left 0.3s ease-in-out;
                width: 280px;
            }

            .admin-sidebar.active {
                left: 0;
            }

            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 40;
                opacity: 0;
                visibility: hidden;
                transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
            }

            .sidebar-overlay.active {
                opacity: 1;
                visibility: visible;
            }

            .main-content {
                margin-left: 0 !important;
            }

            .mobile-header {
                display: flex !important;
            }

            body {
                padding-top: 60px; /* Mobile header yüksekliği için boşluk */
            }

            .flex.h-screen {
                height: calc(100vh - 60px);
            }

            .main-content header {
                display: none; /* Mobilde üst header'ı gizle */
            }

            .main-content main {
                padding-top: 1rem;
            }
        }

        @media (min-width: 769px) {
            .mobile-header {
                display: none;
            }
        }
    </style>
    
    <?php if (isset($additional_head)): ?>
        <?php echo $additional_head; ?>
    <?php endif; ?>
</head>
<body class="bg-gray-50">
    <!-- Mobile Header -->
    <div class="mobile-header fixed top-0 left-0 right-0 bg-white shadow-sm border-b border-gray-200 px-4 py-3 md:hidden z-30">
        <div class="flex items-center justify-between">
            <button id="mobile-sidebar-toggle" class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                <i class="fas fa-bars text-xl"></i>
            </button>
            <h1 class="text-lg font-semibold text-gray-800">
                <?php echo isset($page_title) ? $page_title : 'Admin Panel'; ?>
            </h1>
            <div class="w-10"></div> <!-- Spacer for centering -->
        </div>
    </div>

    <!-- Sidebar Overlay -->
    <div id="sidebar-overlay" class="sidebar-overlay"></div>

    <div class="flex h-screen">
        <!-- Sidebar -->
        <div id="admin-sidebar" class="admin-sidebar w-64 bg-white shadow-lg md:relative md:translate-x-0">
            <!-- Logo -->
            <div class="p-6 border-b border-gray-200">
                <h1 class="text-xl font-bold text-primary-600">
                    <i class="fas fa-cog mr-2"></i>Admin Panel
                </h1>
                <p class="text-sm text-gray-500 mt-1">ToolHane Blog</p>
            </div>
            
            <!-- Navigation -->
            <nav class="mt-6">
                <div class="px-4 space-y-2">
                    <a href="dashboard.php" class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg transition-colors <?php echo basename($_SERVER['PHP_SELF']) === 'dashboard.php' ? 'active' : ''; ?>">
                        <i class="fas fa-tachometer-alt mr-3"></i>
                        Dashboard
                    </a>
                    
                    <a href="posts.php" class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg transition-colors <?php echo basename($_SERVER['PHP_SELF']) === 'posts.php' ? 'active' : ''; ?>">
                        <i class="fas fa-file-alt mr-3"></i>
                        Blog Yazıları
                    </a>
                    
                    <a href="post-add.php" class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg transition-colors <?php echo basename($_SERVER['PHP_SELF']) === 'post-add.php' ? 'active' : ''; ?>">
                        <i class="fas fa-plus mr-3"></i>
                        Yeni Yazı
                    </a>
                    
                    <a href="comments.php" class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg transition-colors <?php echo basename($_SERVER['PHP_SELF']) === 'comments.php' ? 'active' : ''; ?>">
                        <i class="fas fa-comments mr-3"></i>
                        Yorumlar
                        <?php
                        try {
                            $db = Database::getInstance();
                            $pending_comments = $db->count('comments', 'status = ?', ['pending']);
                            if ($pending_comments > 0):
                        ?>
                            <span class="ml-auto bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                                <?php echo $pending_comments; ?>
                            </span>
                        <?php 
                            endif;
                        } catch (Exception $e) {
                            // Ignore error
                        }
                        ?>
                    </a>
                    
                    <a href="categories.php" class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg transition-colors <?php echo basename($_SERVER['PHP_SELF']) === 'categories.php' ? 'active' : ''; ?>">
                        <i class="fas fa-tags mr-3"></i>
                        Kategoriler
                    </a>
                    
                    <a href="settings.php" class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg transition-colors <?php echo basename($_SERVER['PHP_SELF']) === 'settings.php' ? 'active' : ''; ?>">
                        <i class="fas fa-cog mr-3"></i>
                        Ayarlar
                    </a>
                </div>
                
                <!-- Divider -->
                <div class="border-t border-gray-200 mt-6 pt-6">
                    <div class="px-4 space-y-2">
                        <a href="../index.php" target="_blank" class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg transition-colors">
                            <i class="fas fa-external-link-alt mr-3"></i>
                            Blog'u Görüntüle
                        </a>
                        
                        <a href="https://www.toolhane.com" target="_blank" class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg transition-colors">
                            <i class="fas fa-globe mr-3"></i>
                            ToolHane Ana Site
                        </a>
                    </div>
                </div>
            </nav>
        </div>
        
        <!-- Main Content -->
        <div class="main-content flex-1 flex flex-col overflow-hidden md:ml-0">
            <!-- Top Header -->
            <header class="bg-white shadow-sm border-b border-gray-200">
                <div class="flex items-center justify-between px-6 py-4">
                    <div>
                        <h2 class="text-2xl font-semibold text-gray-800">
                            <?php echo isset($page_title) ? $page_title : 'Admin Panel'; ?>
                        </h2>
                        <?php if (isset($page_description)): ?>
                            <p class="text-gray-600 text-sm mt-1"><?php echo $page_description; ?></p>
                        <?php endif; ?>
                    </div>
                    
                    <!-- User Menu -->
                    <div class="flex items-center space-x-4">
                        <!-- Notifications -->
                        <div class="relative">
                            <button class="p-2 text-gray-400 hover:text-gray-600 relative">
                                <i class="fas fa-bell text-xl"></i>
                                <?php
                                try {
                                    $db = Database::getInstance();
                                    $total_notifications = $db->count('comments', 'status = ?', ['pending']);
                                    if ($total_notifications > 0):
                                ?>
                                    <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                                        <?php echo min($total_notifications, 9); ?><?php echo $total_notifications > 9 ? '+' : ''; ?>
                                    </span>
                                <?php 
                                    endif;
                                } catch (Exception $e) {
                                    // Ignore error
                                }
                                ?>
                            </button>
                        </div>
                        
                        <!-- User Dropdown -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="flex items-center space-x-3 text-gray-700 hover:text-gray-900">
                                <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center text-white font-semibold">
                                    <?php echo strtoupper(substr($admin_user['full_name'] ?: $admin_user['username'], 0, 1)); ?>
                                </div>
                                <span class="font-medium"><?php echo clean($admin_user['full_name'] ?: $admin_user['username']); ?></span>
                                <i class="fas fa-chevron-down text-sm"></i>
                            </button>
                            
                            <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                                <a href="profile.php" class="block px-4 py-2 text-gray-700 hover:bg-gray-50">
                                    <i class="fas fa-user mr-2"></i>Profil
                                </a>
                                <a href="settings.php" class="block px-4 py-2 text-gray-700 hover:bg-gray-50">
                                    <i class="fas fa-cog mr-2"></i>Ayarlar
                                </a>
                                <div class="border-t border-gray-200 my-2"></div>
                                <a href="logout.php" class="block px-4 py-2 text-red-600 hover:bg-red-50">
                                    <i class="fas fa-sign-out-alt mr-2"></i>Çıkış Yap
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Flash Messages -->
            <?php $flash_messages = getFlashMessages(); ?>
            <?php if (!empty($flash_messages)): ?>
                <div class="px-6 py-4">
                    <?php foreach ($flash_messages as $message): ?>
                        <div class="mb-4 p-4 rounded-lg <?php echo $message['type'] === 'error' ? 'bg-red-50 border border-red-200 text-red-700' : ($message['type'] === 'success' ? 'bg-green-50 border border-green-200 text-green-700' : 'bg-blue-50 border border-blue-200 text-blue-700'); ?>">
                            <div class="flex items-center">
                                <i class="fas <?php echo $message['type'] === 'error' ? 'fa-exclamation-triangle' : ($message['type'] === 'success' ? 'fa-check-circle' : 'fa-info-circle'); ?> mr-2"></i>
                                <?php echo clean($message['message']); ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            
            <!-- Page Content -->
            <main class="flex-1 overflow-y-auto p-6">
