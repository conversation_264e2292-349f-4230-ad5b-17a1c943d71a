<?php
require_once '../config.php';

// Post ID'yi al
$post_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($post_id <= 0) {
    setFlashMessage('error', 'Geçersiz yazı ID\'si.');
    header('Location: posts.php');
    exit;
}

// Veritabanı bağlantısı
$db = Database::getInstance();

try {
    // Yazıyı al
    $post = $db->fetch("SELECT * FROM posts WHERE id = ?", [$post_id]);
    
    if (!$post) {
        setFlashMessage('error', 'Yazı bulunamadı.');
        header('Location: posts.php');
        exit;
    }
    
    // Silme işlemi onaylandı mı?
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_delete'])) {
        // CSRF token kontrolü
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            setFlashMessage('error', 'Güvenlik hatası. Lütfen tekrar deneyin.');
        } else {
            try {
                $db->beginTransaction();
                
                // İlişkili verileri sil (CASCADE ile otomatik silinecek ama manuel kontrol için)
                $db->delete('post_categories', 'post_id = ?', [$post_id]);
                $db->delete('comments', 'post_id = ?', [$post_id]);
                
                // Yazıyı sil
                $db->delete('posts', 'id = ?', [$post_id]);
                
                $db->commit();
                
                setFlashMessage('success', 'Yazı başarıyla silindi.');
                header('Location: posts.php');
                exit;
                
            } catch (Exception $e) {
                $db->rollback();
                error_log("Post delete error: " . $e->getMessage());
                setFlashMessage('error', 'Yazı silinirken hata oluştu: ' . $e->getMessage());
            }
        }
    }
    
    // İptal edildi mi?
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['cancel'])) {
        header('Location: post-edit.php?id=' . $post_id);
        exit;
    }
    
    // Yazının istatistiklerini al
    $comment_count = $db->count('comments', 'post_id = ?', [$post_id]);
    
} catch (Exception $e) {
    error_log("Post delete fetch error: " . $e->getMessage());
    setFlashMessage('error', 'Yazı yüklenirken hata oluştu.');
    header('Location: posts.php');
    exit;
}

// Sayfa bilgileri
$page_title = 'Yazı Sil';
$page_description = 'Blog yazısını kalıcı olarak sil';

// Header'ı dahil et
include 'includes/header.php';
?>

<!-- Post Delete Confirmation -->
<div class="max-w-2xl mx-auto">
    <div class="bg-white rounded-lg shadow p-6">
        <!-- Warning Header -->
        <div class="flex items-center mb-6">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                </div>
            </div>
            <div class="ml-4">
                <h1 class="text-xl font-bold text-gray-900">Yazıyı Sil</h1>
                <p class="text-gray-600">Bu işlem geri alınamaz!</p>
            </div>
        </div>
        
        <!-- Post Info -->
        <div class="bg-gray-50 rounded-lg p-4 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-2">
                <?php echo clean($post['title']); ?>
            </h2>
            <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                    <span class="text-gray-600">Durum:</span>
                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $post['status'] === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'; ?>">
                        <?php echo $post['status'] === 'published' ? 'Yayında' : 'Taslak'; ?>
                    </span>
                </div>
                <div>
                    <span class="text-gray-600">Oluşturulma:</span>
                    <span class="ml-2"><?php echo formatDate($post['created_at'], 'd.m.Y H:i'); ?></span>
                </div>
                <div>
                    <span class="text-gray-600">Görüntülenme:</span>
                    <span class="ml-2"><?php echo number_format($post['views']); ?></span>
                </div>
                <div>
                    <span class="text-gray-600">Yorum:</span>
                    <span class="ml-2"><?php echo $comment_count; ?> adet</span>
                </div>
            </div>
            
            <?php if (!empty($post['excerpt'])): ?>
                <div class="mt-4">
                    <span class="text-gray-600">Özet:</span>
                    <p class="text-gray-700 mt-1"><?php echo clean($post['excerpt']); ?></p>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Warning Message -->
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-red-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">
                        Dikkat! Bu işlem geri alınamaz.
                    </h3>
                    <div class="mt-2 text-sm text-red-700">
                        <ul class="list-disc list-inside space-y-1">
                            <li>Yazı kalıcı olarak silinecek</li>
                            <?php if ($comment_count > 0): ?>
                                <li><?php echo $comment_count; ?> adet yorum da silinecek</li>
                            <?php endif; ?>
                            <li>Kategori ilişkileri silinecek</li>
                            <li>Bu işlem geri alınamaz</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Confirmation Form -->
        <form method="POST" class="space-y-4">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
            
            <!-- Confirmation Checkbox -->
            <div class="flex items-start">
                <div class="flex items-center h-5">
                    <input id="confirm_checkbox" 
                           type="checkbox" 
                           required
                           class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded">
                </div>
                <div class="ml-3 text-sm">
                    <label for="confirm_checkbox" class="font-medium text-gray-700">
                        Bu yazıyı kalıcı olarak silmek istediğimi onaylıyorum
                    </label>
                    <p class="text-gray-500">Bu işlemin geri alınamayacağını anlıyorum.</p>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row-reverse sm:space-x-reverse sm:space-x-3 space-y-3 sm:space-y-0">
                <button type="submit" 
                        name="confirm_delete"
                        id="delete-button"
                        disabled
                        class="w-full sm:w-auto bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed">
                    <i class="fas fa-trash mr-2"></i>Yazıyı Sil
                </button>
                
                <button type="submit" 
                        name="cancel"
                        class="w-full sm:w-auto bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                    <i class="fas fa-times mr-2"></i>İptal
                </button>
                
                <a href="posts.php" 
                   class="w-full sm:w-auto bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors text-center">
                    <i class="fas fa-arrow-left mr-2"></i>Yazılara Dön
                </a>
            </div>
        </form>
        
        <!-- Alternative Actions -->
        <div class="mt-6 pt-6 border-t border-gray-200">
            <h3 class="text-sm font-medium text-gray-700 mb-3">Alternatif İşlemler</h3>
            <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
                <a href="post-edit.php?id=<?php echo $post['id']; ?>" 
                   class="text-primary-600 hover:text-primary-700 text-sm font-medium">
                    <i class="fas fa-edit mr-1"></i>Yazıyı Düzenle
                </a>
                
                <?php if ($post['status'] === 'published'): ?>
                    <form method="POST" action="posts.php" class="inline">
                        <input type="hidden" name="bulk_action" value="draft">
                        <input type="hidden" name="selected[]" value="<?php echo $post['id']; ?>">
                        <button type="submit" class="text-yellow-600 hover:text-yellow-700 text-sm font-medium">
                            <i class="fas fa-eye-slash mr-1"></i>Taslak Yap
                        </button>
                    </form>
                <?php else: ?>
                    <form method="POST" action="posts.php" class="inline">
                        <input type="hidden" name="bulk_action" value="publish">
                        <input type="hidden" name="selected[]" value="<?php echo $post['id']; ?>">
                        <button type="submit" class="text-green-600 hover:text-green-700 text-sm font-medium">
                            <i class="fas fa-eye mr-1"></i>Yayınla
                        </button>
                    </form>
                <?php endif; ?>
                
                <a href="../post.php?slug=<?php echo clean($post['slug']); ?>" 
                   target="_blank" 
                   class="text-gray-600 hover:text-gray-700 text-sm font-medium">
                    <i class="fas fa-external-link-alt mr-1"></i>Yazıyı Görüntüle
                </a>
            </div>
        </div>
    </div>
</div>

<?php 
$additional_scripts = '
<script>
// Enable/disable delete button based on checkbox
document.getElementById("confirm_checkbox").addEventListener("change", function() {
    const deleteButton = document.getElementById("delete-button");
    deleteButton.disabled = !this.checked;
    
    if (this.checked) {
        deleteButton.classList.remove("bg-gray-400", "cursor-not-allowed");
        deleteButton.classList.add("bg-red-600", "hover:bg-red-700");
    } else {
        deleteButton.classList.add("bg-gray-400", "cursor-not-allowed");
        deleteButton.classList.remove("bg-red-600", "hover:bg-red-700");
    }
});

// Confirm deletion with additional prompt
document.querySelector("form").addEventListener("submit", function(e) {
    if (e.submitter && e.submitter.name === "confirm_delete") {
        const confirmed = confirm("Son kez soruyorum: Bu yazıyı kalıcı olarak silmek istediğinizden emin misiniz?");
        if (!confirmed) {
            e.preventDefault();
            return false;
        }
        
        // Change button text to show processing
        e.submitter.disabled = true;
        e.submitter.innerHTML = "<i class=\"fas fa-spinner fa-spin mr-2\"></i>Siliniyor...";
    }
});
</script>
';

include 'includes/footer.php'; 
?>
