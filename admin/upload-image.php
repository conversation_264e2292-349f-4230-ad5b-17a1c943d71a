
<?php
// Modern görsel yükleme: post_images tablosuna kayıt
ini_set('error_log', __DIR__ . '/error_log');
require_once '../config.php';
require_once '../includes/ImageUploader.php';
require_once __DIR__ . '/includes/auth.php';
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'error' => 'Geçersiz istek']);
    exit;
}

try {
    if (!function_exists('clean')) throw new Exception('clean() fonksiyonu bulunamadı');
    if (!class_exists('Database')) throw new Exception('Database sınıfı bulunamadı');

    $uploader = new ImageUploader();
    $db = Database::getInstance();

    if (!isset($_FILES['image'])) throw new Exception('Dosya seçilmedi');
    if ($_FILES['image']['error'] !== UPLOAD_ERR_OK) throw new Exception('Dosya yükleme hatası: ' . $_FILES['image']['error']);

    $postId = isset($_POST['post_id']) && $_POST['post_id'] ? (int)$_POST['post_id'] : null;
    $altText = isset($_POST['alt_text']) ? clean($_POST['alt_text']) : '';
    $caption = isset($_POST['caption']) ? clean($_POST['caption']) : '';
    $isFeatured = isset($_POST['is_featured']) ? (int)$_POST['is_featured'] : 0;

    $result = $uploader->uploadImage($_FILES['image'], $postId, $altText, $caption);

    if (!$result['success']) throw new Exception($result['error']);

    // post_images tablosuna kaydet
    $filename = $result['filename'];
    $original_filename = $_FILES['image']['name'];
    $file_path = $result['path'];
    $file_size = $_FILES['image']['size'];
    $mime_type = $_FILES['image']['type'];
    $width = isset($result['width']) ? $result['width'] : 0;
    $height = isset($result['height']) ? $result['height'] : 0;
    $created_at = date('Y-m-d H:i:s');

    // Sadece postId varsa kaydet
    if ($postId) {
        $db->insert('post_images', [
            'post_id' => $postId,
            'filename' => $filename,
            'original_filename' => $original_filename,
            'file_path' => $file_path,
            'file_size' => $file_size,
            'mime_type' => $mime_type,
            'width' => $width,
            'height' => $height,
            'alt_text' => $altText,
            'caption' => $caption,
            'is_featured' => $isFeatured,
            'sort_order' => 0,
            'created_at' => $created_at,
            'updated_at' => $created_at
        ]);
    }

    echo json_encode($result);

} catch (Exception $e) {
    error_log('Upload error: ' . $e->getMessage());
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}
?>
