# ToolHane Blog Sistemi

ToolHane.com sitesinin tasarımına %100 uyumlu, modern PHP tabanlı blog sistemi.

## Özellikler

### 🎨 Tasarım
- ToolHane.com ile birebir uyumlu tasarım
- Tailwind CSS ile modern ve responsive arayüz
- Koyu/açık mod desteği
- Mobil uyumlu tasarım

### 📝 Blog Yönetimi
- Modern WYSIWYG editör (TinyMCE)
- <PERSON><PERSON><PERSON> sistemi
- SEO dostu URL yapısı
- Meta title ve description desteği
- Görüntülenme sayacı

### 💬 Yorum Sistemi
- Moderasyon sistemi (onay bekleyen yorumlar)
- Spam koruması
- E-posta bildirimleri (opsiyonel)

### 🔐 Güvenlik
- PDO + Prepared Statements (SQL Injection koruması)
- XSS filtreleme
- CSRF token koruması
- Bcrypt şifre hashleme
- Session tabanlı admin girişi

### 🔍 Arama
- Gelişmiş arama sistemi
- AJAX destekli anlık arama
- Arama sonuçlarında vurgulama

### 📊 Admin Paneli
- Modern dashboard
- İstatistikler ve grafikler
- Toplu işlemler
- Responsive admin arayüzü

## Sistem Gereksinimleri

- PHP 8.0 veya üzeri
- MySQL 5.7 veya üzeri
- Web sunucusu (Apache/Nginx)
- FTP erişimi

## Kurulum

### 1. Dosyaları Yükleme
```bash
# Tüm dosyaları public_html/blog klasörüne yükleyin
```

### 2. Veritabanı Kurulumu
1. Tarayıcınızda `https://blog.toolhane.com/install.php` adresine gidin
2. Kurulum otomatik olarak tamamlanacaktır
3. Kurulum tamamlandıktan sonra `install.php` dosyasını silin

### 3. Admin Girişi
- URL: `https://blog.toolhane.com/admin/login.php`
- Kullanıcı adı: `admin`
- Şifre: `admin123`

**⚠️ Güvenlik için ilk girişten sonra şifrenizi değiştirin!**

## Dosya Yapısı

```
blog/
├── admin/                  # Admin paneli
│   ├── includes/          # Admin ortak dosyalar
│   ├── login.php         # Admin giriş
│   ├── dashboard.php     # Ana dashboard
│   ├── posts.php         # Yazı listesi
│   ├── post-add.php      # Yeni yazı
│   ├── post-edit.php     # Yazı düzenleme
│   ├── post-delete.php   # Yazı silme
│   ├── comments.php      # Yorum yönetimi
│   └── logout.php        # Çıkış
├── assets/               # CSS, JS, resimler
│   ├── css/
│   ├── js/
│   └── images/
├── includes/             # Ortak dosyalar
│   ├── header.php
│   ├── footer.php
│   ├── functions.php
│   └── Database.php
├── config.php           # Konfigürasyon
├── index.php           # Ana sayfa
├── post.php            # Tekil yazı
├── category.php        # Kategori sayfası
├── search.php          # Arama sayfası
├── 404.php             # 404 hatası
├── 500.php             # 500 hatası
└── install.php         # Kurulum dosyası
```

## Veritabanı Yapısı

### Tablolar
- `users` - Kullanıcılar (admin)
- `posts` - Blog yazıları
- `comments` - Yorumlar
- `categories` - Kategoriler
- `post_categories` - Yazı-kategori ilişkisi

## Konfigürasyon

### config.php Ayarları
```php
// Veritabanı
define('DB_HOST', 'localhost');
define('DB_NAME', 'toolhane_blog');
define('DB_USER', 'toolhane');
define('DB_PASS', '85f2sQv9kF');

// Site
define('SITE_NAME', 'ToolHane Blog');
define('SITE_URL', 'https://blog.toolhane.com');

// Sayfalama
define('POSTS_PER_PAGE', 10);
```

## Kullanım

### Yeni Yazı Ekleme
1. Admin paneline giriş yapın
2. "Yeni Yazı Ekle" butonuna tıklayın
3. Başlık, içerik ve kategorileri doldurun
4. "Kaydet" butonuna tıklayın

### Yorum Yönetimi
1. Admin panelinde "Yorumlar" bölümüne gidin
2. Bekleyen yorumları onaylayın veya silin
3. Spam yorumları işaretleyin

### Kategori Yönetimi
1. Admin panelinde "Kategoriler" bölümüne gidin
2. Yeni kategori ekleyin veya mevcut kategorileri düzenleyin

## Güvenlik Önerileri

1. **Şifre Değiştirme**: İlk girişten sonra admin şifresini değiştirin
2. **Dosya İzinleri**: Dosya izinlerini 644, klasör izinlerini 755 yapın
3. **SSL Sertifikası**: HTTPS kullanın
4. **Yedekleme**: Düzenli veritabanı yedeklemeleri alın
5. **Güncelleme**: PHP ve MySQL'i güncel tutun

## Sorun Giderme

### Yaygın Sorunlar

**1. Veritabanı Bağlantı Hatası**
- `config.php` dosyasındaki veritabanı bilgilerini kontrol edin
- Veritabanı kullanıcısının gerekli izinlere sahip olduğundan emin olun

**2. 404 Hatası**
- `.htaccess` dosyasının doğru yapılandırıldığından emin olun
- URL Rewrite modülünün aktif olduğunu kontrol edin

**3. Admin Paneline Erişim Sorunu**
- Session ayarlarını kontrol edin
- Çerezlerin aktif olduğundan emin olun

**4. Dosya Yükleme Sorunu**
- `assets/images/uploads/` klasörünün yazılabilir olduğundan emin olun
- PHP `upload_max_filesize` ayarını kontrol edin

## Destek

Herhangi bir sorun yaşarsanız:
1. Hata loglarını kontrol edin
2. PHP error_log dosyasını inceleyin
3. Veritabanı bağlantısını test edin

## Lisans

Bu proje MIT lisansı altında lisanslanmıştır.

## Katkıda Bulunma

1. Projeyi fork edin
2. Yeni bir branch oluşturun
3. Değişikliklerinizi yapın
4. Pull request gönderin

---

**ToolHane Blog Sistemi** - Modern, güvenli ve kullanıcı dostu PHP blog sistemi.
