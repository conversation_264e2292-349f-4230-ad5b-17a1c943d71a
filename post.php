<?php
require_once 'config.php';

// Slug parametresini al
$slug = isset($_GET['slug']) ? clean($_GET['slug']) : '';

if (empty($slug)) {
    header('Location: index.php');
    exit;
}

// Veritabanı bağlantısı
$db = Database::getInstance();

try {
    // Blog yazısını al
    $post_sql = "
        SELECT p.*, u.full_name as author_name, u.username as author_username
        FROM posts p
        LEFT JOIN users u ON p.author_id = u.id
        WHERE p.slug = ? AND p.status = 'published'
    ";
    $post = $db->fetch($post_sql, [$slug]);

    if (!$post) {
        header('HTTP/1.0 404 Not Found');
        include '404.php';
        exit;
    }
    
    // Görüntülenme sayısını artır
    try {
        $db->update('posts', ['views' => $post['views'] + 1], 'id = ?', [$post['id']]);
    } catch (Exception $e) {
        error_log("View count update error: " . $e->getMessage());
    }
        // Postun öne çıkan görselini post_images tablosundan al
        $main_image = $db->fetch("SELECT * FROM post_images WHERE post_id = ? AND is_featured = 1 ORDER BY id ASC LIMIT 1", [$post['id']]);

    // Yorumları al (onaylanmış olanlar)
    $comments_sql = "
        SELECT * FROM comments
        WHERE post_id = ? AND status = 'approved'
        ORDER BY created_at ASC
    ";
    $comments = $db->fetchAll($comments_sql, [$post['id']]);
    
    // İlgili yazıları al (aynı kategorideki diğer yazılar)
    $related_posts_sql = "
        SELECT DISTINCT p.*, u.full_name as author_name
        FROM posts p
        LEFT JOIN users u ON p.author_id = u.id
        LEFT JOIN post_categories pc ON p.id = pc.post_id
        WHERE p.id != ? AND p.status = 'published'
        AND pc.category_id IN (
            SELECT category_id FROM post_categories WHERE post_id = ?
        )
        ORDER BY p.created_at DESC
        LIMIT 3
    ";
    $related_posts = $db->fetchAll($related_posts_sql, [$post['id'], $post['id']]);
    
    // Eğer ilgili yazı yoksa, son yazıları göster
    if (empty($related_posts)) {
        $related_posts_sql = "
            SELECT p.*, u.full_name as author_name
            FROM posts p
            LEFT JOIN users u ON p.author_id = u.id
            WHERE p.id != ? AND p.status = 'published'
            ORDER BY p.created_at DESC
            LIMIT 3
        ";
        $related_posts = $db->fetchAll($related_posts_sql, [$post['id']]);
    }
    
    // Yorum gönderme işlemi
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_comment'])) {
        $comment_data = [
            'author_name' => clean($_POST['author_name'] ?? ''),
            'author_email' => clean($_POST['author_email'] ?? ''),
            'author_website' => clean($_POST['author_website'] ?? ''),
            'content' => clean($_POST['content'] ?? ''),
        ];
        
        $errors = [];
        
        // Validasyon
        if (empty($comment_data['author_name'])) {
            $errors[] = 'İsim alanı zorunludur.';
        }
        
        if (empty($comment_data['author_email'])) {
            $errors[] = 'E-posta alanı zorunludur.';
        } elseif (!filter_var($comment_data['author_email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Geçerli bir e-posta adresi girin.';
        }
        
        if (empty($comment_data['content'])) {
            $errors[] = 'Yorum içeriği zorunludur.';
        }
        
        // CSRF token kontrolü
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            $errors[] = 'Güvenlik hatası. Lütfen tekrar deneyin.';
        }
        
        if (empty($errors)) {
            // Yorumu veritabanına ekle
            $comment_insert_data = array_merge($comment_data, [
                'post_id' => $post['id'],
                'status' => 'pending', // Yorumlar onaylanmadan görünmez
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
            
            $db->insert('comments', $comment_insert_data);
            setFlashMessage('success', 'Yorumunuz başarıyla gönderildi. Onaylandıktan sonra görüntülenecektir.');
            
            // Aynı sayfaya yönlendir (PRG pattern)
            header('Location: post.php?slug=' . $slug . '#comments');
            exit;
        } else {
            foreach ($errors as $error) {
                setFlashMessage('error', $error);
            }
        }
    }
    
} catch (Exception $e) {
    error_log("Post page error: " . $e->getMessage());
    header('HTTP/1.0 500 Internal Server Error');
    include '500.php';
    exit;
}

// Sayfa bilgileri
$page_title = $post['meta_title'] ?: $post['title'];
$page_description = $post['meta_description'] ?: excerpt($post['content'], 160);

// Breadcrumb
$breadcrumb_items = [
    ['title' => 'Ana Sayfa', 'url' => 'index.php'],
    ['title' => $post['title'], 'url' => '']
];

// Header'ı dahil et
include 'includes/header.php';
?>

<!-- Breadcrumb -->
<div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <?php echo getBreadcrumb($breadcrumb_items); ?>
    </div>
</div>

<!-- Main Content -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Post Content -->
        <div class="lg:col-span-2">
            <article class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
                <!-- Featured Image -->
                <?php if (!empty($main_image) && !empty($main_image['file_path'])): ?>
                    <div class="w-full">
                        <img src="/<?php echo clean($main_image['file_path']); ?>"
                             alt="<?php echo clean($main_image['alt_text'] ?: $post['title']); ?>"
                             class="w-full h-64 md:h-96 object-cover">
                    </div>
                <?php endif; ?>

                <!-- Post Header -->
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <h1 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                        <?php echo clean($post['title']); ?>
                    </h1>
                    
                    <div class="flex flex-wrap items-center text-sm text-gray-500 dark:text-gray-400 space-x-6">
                        <span class="flex items-center">
                            <i class="fas fa-user mr-2"></i>
                            <?php echo clean($post['author_name'] ?: $post['author_username']); ?>
                        </span>
                        <span class="flex items-center">
                            <i class="fas fa-calendar mr-2"></i>
                            <?php echo formatDate($post['created_at'], 'd F Y'); ?>
                        </span>
                        <span class="flex items-center">
                            <i class="fas fa-eye mr-2"></i>
                            <?php echo number_format($post['views']); ?> görüntülenme
                        </span>
                        <span class="flex items-center">
                            <i class="fas fa-comments mr-2"></i>
                            <?php echo count($comments); ?> yorum
                        </span>
                    </div>
                </div>
                
                <!-- Post Content -->
                <div class="p-6 post-content prose prose-lg max-w-none dark:prose-invert">
                    <?php echo $post['content']; ?>
                </div>
                
                <!-- Post Footer -->
                <div class="p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                            Son güncelleme: <?php echo formatDate($post['updated_at'], 'd.m.Y H:i'); ?>
                        </div>
                        
                        <!-- Social Share Buttons -->
                        <div class="flex space-x-2">
                            <a href="https://twitter.com/intent/tweet?text=<?php echo urlencode($post['title']); ?>&url=<?php echo urlencode(SITE_URL . '/post.php?slug=' . $post['slug']); ?>" 
                               target="_blank" 
                               class="bg-blue-500 text-white px-3 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(SITE_URL . '/post.php?slug=' . $post['slug']); ?>" 
                               target="_blank" 
                               class="bg-blue-700 text-white px-3 py-2 rounded-lg hover:bg-blue-800 transition-colors">
                                <i class="fab fa-facebook"></i>
                            </a>
                            <button onclick="copyToClipboard('<?php echo SITE_URL . '/post.php?slug=' . $post['slug']; ?>')" 
                                    class="bg-gray-600 text-white px-3 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                                <i class="fas fa-link"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </article>
            
            <!-- Comments Section -->
            <div id="comments" class="mt-8 bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                    Yorumlar (<?php echo count($comments); ?>)
                </h3>
                
                <!-- Comments List -->
                <?php if (empty($comments)): ?>
                    <div class="text-center py-8">
                        <i class="fas fa-comments text-4xl text-gray-300 dark:text-gray-600 mb-4"></i>
                        <p class="text-gray-500 dark:text-gray-400">
                            Henüz yorum yapılmamış. İlk yorumu siz yapın!
                        </p>
                    </div>
                <?php else: ?>
                    <div class="space-y-6 mb-8">
                        <?php foreach ($comments as $comment): ?>
                            <div id="comment-<?php echo $comment['id']; ?>" class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <div class="flex items-start space-x-4">
                                    <div class="flex-shrink-0">
                                        <div class="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center text-white font-semibold">
                                            <?php echo strtoupper(substr($comment['author_name'], 0, 1)); ?>
                                        </div>
                                    </div>
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-2 mb-2">
                                            <h4 class="font-semibold text-gray-900 dark:text-white">
                                                <?php if (!empty($comment['author_website'])): ?>
                                                    <a href="<?php echo clean($comment['author_website']); ?>" 
                                                       target="_blank" 
                                                       class="hover:text-primary-600 dark:hover:text-primary-400">
                                                        <?php echo clean($comment['author_name']); ?>
                                                    </a>
                                                <?php else: ?>
                                                    <?php echo clean($comment['author_name']); ?>
                                                <?php endif; ?>
                                            </h4>
                                            <span class="text-sm text-gray-500 dark:text-gray-400">
                                                <?php echo timeAgo($comment['created_at']); ?>
                                            </span>
                                        </div>
                                        <div class="text-gray-700 dark:text-gray-300 leading-relaxed">
                                            <?php echo nl2br(clean($comment['content'])); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
                
                <!-- Comment Form -->
                <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                    <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        Yorum Yap
                    </h4>
                    
                    <form method="POST" class="space-y-4">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="author_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    İsim *
                                </label>
                                <input type="text" 
                                       id="author_name" 
                                       name="author_name" 
                                       required
                                       value="<?php echo isset($_POST['author_name']) ? clean($_POST['author_name']) : ''; ?>"
                                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white">
                            </div>
                            
                            <div>
                                <label for="author_email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    E-posta *
                                </label>
                                <input type="email" 
                                       id="author_email" 
                                       name="author_email" 
                                       required
                                       value="<?php echo isset($_POST['author_email']) ? clean($_POST['author_email']) : ''; ?>"
                                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white">
                            </div>
                        </div>
                        
                        <div>
                            <label for="author_website" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Website (İsteğe bağlı)
                            </label>
                            <input type="url" 
                                   id="author_website" 
                                   name="author_website"
                                   value="<?php echo isset($_POST['author_website']) ? clean($_POST['author_website']) : ''; ?>"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white">
                        </div>
                        
                        <div>
                            <label for="content" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Yorum *
                            </label>
                            <textarea id="content" 
                                      name="content" 
                                      rows="4" 
                                      required
                                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white"><?php echo isset($_POST['content']) ? clean($_POST['content']) : ''; ?></textarea>
                        </div>
                        
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                            * E-posta adresiniz yayınlanmayacaktır. Yorumunuz onaylandıktan sonra görüntülenecektir.
                        </div>
                        
                        <button type="submit" 
                                name="submit_comment"
                                class="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors">
                            Yorum Gönder
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- İlgili Yazılar -->
            <?php if (!empty($related_posts)): ?>
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 pb-2 border-b-2 border-primary-600">
                        İlgili Yazılar
                    </h3>
                    <div class="space-y-4">
                        <?php foreach ($related_posts as $related_post): ?>
                            <div class="flex space-x-3">
                                <div class="flex-1">
                                    <h4 class="text-sm font-medium text-gray-900 dark:text-white leading-tight mb-1">
                                        <a href="post.php?slug=<?php echo clean($related_post['slug']); ?>" 
                                           class="hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                                            <?php echo clean($related_post['title']); ?>
                                        </a>
                                    </h4>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">
                                        <?php echo formatDate($related_post['created_at'], 'd.m.Y'); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Ana Sayfaya Dön -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 text-center">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    Daha Fazla İçerik
                </h3>
                <p class="text-gray-600 dark:text-gray-300 mb-4">
                    Diğer blog yazılarımızı keşfetmek için ana sayfayı ziyaret edin.
                </p>
                <a href="index.php" 
                   class="inline-block bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors">
                    Ana Sayfa
                </a>
            </div>
        </div>
    </div>
</div>

<?php 
// Copy to clipboard fonksiyonu için JavaScript ekle
$additional_scripts = '
<script>
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showNotification("Link panoya kopyalandı!", "success");
        });
    } else {
        const textArea = document.createElement("textarea");
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand("copy");
        document.body.removeChild(textArea);
        showNotification("Link panoya kopyalandı!", "success");
    }
}

function showNotification(message, type) {
    const notification = document.createElement("div");
    notification.className = "fixed top-20 right-4 z-50 px-4 py-2 rounded-lg text-white font-medium transition-all duration-300 transform translate-x-full";
    notification.style.backgroundColor = type === "success" ? "#10b981" : "#ef4444";
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.classList.remove("translate-x-full");
    }, 100);
    
    setTimeout(() => {
        notification.classList.add("translate-x-full");
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}
</script>
';

include 'includes/footer.php'; 
?>
